import{m as h,j as e,L as g}from"./app-BWHLaRS2.js";import{I as d}from"./input-error-Cz-64hVg.js";import{T as o}from"./text-link-BJC5FN6k.js";import{B as b}from"./button-CUovRkQ3.js";import{C as j}from"./checkbox-B6mGW3Gq.js";import{I as n}from"./input-B4rX1XfI.js";import{L as i}from"./label-DWihlnjb.js";import{A as f}from"./auth-layout-BCFIpUOr.js";import{L as v}from"./loader-circle-Bb-8g7No.js";import"./index-BDjL_iy4.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./book-open-DxiZ0b6m.js";import"./card-3ac6awgC.js";function D({status:m,canResetPassword:c}){const{data:s,setData:a,post:x,processing:t,errors:l,reset:p}=h({email:"",password:"",remember:!1}),u=r=>{r.preventDefault(),x(route("login"),{onFinish:()=>p("password")})};return e.jsxs(f,{title:"Log in to your account",description:"Enter your email and password below to log in",children:[e.jsx(g,{title:"Log in"}),e.jsxs("form",{className:"flex flex-col gap-6",onSubmit:u,children:[e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(i,{htmlFor:"email",children:"Email address"}),e.jsx(n,{id:"email",type:"email",required:!0,autoFocus:!0,tabIndex:1,autoComplete:"email",value:s.email,onChange:r=>a("email",r.target.value),placeholder:"<EMAIL>"}),e.jsx(d,{message:l.email})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(i,{htmlFor:"password",children:"Password"}),c&&e.jsx(o,{href:route("password.request"),className:"ml-auto text-sm",tabIndex:5,children:"Forgot password?"})]}),e.jsx(n,{id:"password",type:"password",required:!0,tabIndex:2,autoComplete:"current-password",value:s.password,onChange:r=>a("password",r.target.value),placeholder:"Password"}),e.jsx(d,{message:l.password})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(j,{id:"remember",name:"remember",checked:s.remember,onClick:()=>a("remember",!s.remember),tabIndex:3}),e.jsx(i,{htmlFor:"remember",children:"Remember me"})]}),e.jsxs(b,{type:"submit",className:"mt-6 w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5",tabIndex:4,disabled:t,children:[t&&e.jsx(v,{className:"h-4 w-4 animate-spin mr-2"}),t?"Signing in...":"Sign In"]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("span",{className:"w-full border-t border-gray-200 dark:border-gray-700"})}),e.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:e.jsx("span",{className:"bg-white dark:bg-gray-800 px-2 text-gray-500 dark:text-gray-400",children:"New to SOP Quotes?"})})]}),e.jsx("div",{className:"text-center",children:e.jsx(o,{href:route("register"),tabIndex:5,className:"text-blue-600 hover:text-blue-700 font-medium",children:"Create an account"})}),e.jsx("div",{className:"text-center pt-4 border-t border-gray-100 dark:border-gray-700",children:e.jsx(o,{href:route("home"),tabIndex:6,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 text-sm",children:"← Back to Home"})})]}),m&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:m})]})}export{D as default};
