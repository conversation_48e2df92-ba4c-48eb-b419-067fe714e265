<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Faith',
                'slug' => 'faith',
                'description' => 'Quotes about faith, trust, and belief in God',
                'color' => '#3B82F6',
                'icon' => 'heart',
                'sort_order' => 1,
            ],
            [
                'name' => 'Hope',
                'slug' => 'hope',
                'description' => 'Inspirational quotes about hope and perseverance',
                'color' => '#10B981',
                'icon' => 'sunrise',
                'sort_order' => 2,
            ],
            [
                'name' => 'Love',
                'slug' => 'love',
                'description' => 'Quotes about God\'s love and loving others',
                'color' => '#EF4444',
                'icon' => 'heart-handshake',
                'sort_order' => 3,
            ],
            [
                'name' => 'Prayer',
                'slug' => 'prayer',
                'description' => 'Quotes about prayer and communion with God',
                'color' => '#8B5CF6',
                'icon' => 'hands',
                'sort_order' => 4,
            ],
            [
                'name' => 'Peace',
                'slug' => 'peace',
                'description' => 'Quotes about finding peace in God',
                'color' => '#06B6D4',
                'icon' => 'dove',
                'sort_order' => 5,
            ],
            [
                'name' => 'Wisdom',
                'slug' => 'wisdom',
                'description' => 'Biblical wisdom and understanding',
                'color' => '#F59E0B',
                'icon' => 'lightbulb',
                'sort_order' => 6,
            ],
            [
                'name' => 'Strength',
                'slug' => 'strength',
                'description' => 'Quotes about finding strength in God',
                'color' => '#DC2626',
                'icon' => 'shield',
                'sort_order' => 7,
            ],
            [
                'name' => 'Joy',
                'slug' => 'joy',
                'description' => 'Quotes about joy and happiness in Christ',
                'color' => '#F97316',
                'icon' => 'smile',
                'sort_order' => 8,
            ],
            [
                'name' => 'Forgiveness',
                'slug' => 'forgiveness',
                'description' => 'Quotes about God\'s forgiveness and forgiving others',
                'color' => '#84CC16',
                'icon' => 'heart-crack',
                'sort_order' => 9,
            ],
            [
                'name' => 'Salvation',
                'slug' => 'salvation',
                'description' => 'Quotes about salvation through Jesus Christ',
                'color' => '#6366F1',
                'icon' => 'cross',
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
