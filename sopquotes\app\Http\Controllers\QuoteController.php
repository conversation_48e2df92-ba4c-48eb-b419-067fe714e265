<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Quote;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class QuoteController extends Controller
{
    /**
     * Display a listing of quotes.
     */
    public function index(Request $request): Response
    {
        $query = Quote::with(['category', 'creator'])
            ->active()
            ->latest();

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by featured
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Filter by user's favorites
        if ($request->boolean('favorites')) {
            if (auth()->check()) {
                // Authenticated users - use database favorites
                $query->whereHas('favorites', function ($q) {
                    $q->where('user_id', auth()->id());
                });
            } else {
                // Guest users - use session favorites
                $sessionFavorites = session()->get('favorites', []);
                if (!empty($sessionFavorites)) {
                    $query->whereIn('id', $sessionFavorites);
                } else {
                    // No session favorites, return empty result
                    $query->where('id', -1);
                }
            }
        }

        $quotes = $query->paginate(12)->withQueryString();

        // Add favorite status for all users
        if (auth()->check()) {
            // Authenticated users - check database favorites
            $quotes->getCollection()->transform(function ($quote) {
                $quote->is_favorited = $quote->favorites()
                    ->where('user_id', auth()->id())
                    ->exists();
                return $quote;
            });
        } else {
            // Guest users - check session favorites
            $sessionFavorites = session()->get('favorites', []);
            $quotes->getCollection()->transform(function ($quote) use ($sessionFavorites) {
                $quote->is_favorited = in_array($quote->id, $sessionFavorites);
                return $quote;
            });
        }

        $categories = Category::active()->ordered()->get();

        // Debug: Log categories to see what's being fetched
        \Log::info('Categories fetched for quotes index:', [
            'count' => $categories->count(),
            'categories' => $categories->pluck('name', 'id')->toArray()
        ]);

        return Inertia::render('quotes/index', [
            'quotes' => $quotes,
            'categories' => $categories,
            'filters' => (object) $request->only(['category', 'search', 'featured', 'favorites']),
        ]);
    }





    /**
     * Display the specified quote.
     */
    public function show(Quote $quote): Response
    {
        $quote->load(['category', 'creator']);

        // Record reading history for authenticated users
        if (auth()->check()) {
            auth()->user()->readingHistory()->updateOrCreate([
                'readable_type' => Quote::class,
                'readable_id' => $quote->id,
            ], [
                'viewed_at' => now(),
            ]);
        }

        // Get statistics
        $favoritesCount = $quote->favorites()->count();
        $viewsCount = $quote->readingHistory()->count();

        // Get related quotes with better variety
        $relatedQuotes = Quote::active()
            ->where('id', '!=', $quote->id)
            ->where(function ($query) use ($quote) {
                $query->where('category_id', $quote->category_id)
                    ->orWhere('author', $quote->author)
                    ->orWhere('source', $quote->source);
            })
            ->with(['category', 'creator'])
            ->limit(6)
            ->get();

        // Get quotes from same category if we don't have enough related quotes
        if ($relatedQuotes->count() < 3) {
            $additionalQuotes = Quote::active()
                ->where('id', '!=', $quote->id)
                ->whereNotIn('id', $relatedQuotes->pluck('id'))
                ->where('category_id', $quote->category_id)
                ->with(['category', 'creator'])
                ->limit(6 - $relatedQuotes->count())
                ->get();

            $relatedQuotes = $relatedQuotes->merge($additionalQuotes);
        }

        return Inertia::render('quotes/show', [
            'quote' => $quote,
            'isFavorited' => auth()->check()
                ? $quote->isFavoritedBy(auth()->user())
                : in_array($quote->id, session()->get('favorites', [])),
            'relatedQuotes' => $relatedQuotes,
            'statistics' => [
                'favorites_count' => $favoritesCount,
                'views_count' => $viewsCount,
                'created_date' => $quote->created_at->format('F j, Y'),
                'word_count' => str_word_count($quote->text),
                'reading_time' => ceil(str_word_count($quote->text) / 200), // Average reading speed
            ],
        ]);
    }



    /**
     * Get daily quote.
     */
    public function daily(): Response
    {
        $quote = Quote::active()
            ->featured()
            ->inRandomOrder()
            ->first();

        if (!$quote) {
            $quote = Quote::active()
                ->inRandomOrder()
                ->first();
        }

        return Inertia::render('quotes/daily', [
            'quote' => $quote?->load(['category', 'creator']),
        ]);
    }

    /**
     * Toggle favorite status for a quote.
     */
    public function toggleFavorite(Quote $quote)
    {
        $user = auth()->user();

        if ($user) {
            // Authenticated user - use database favorites
            $favorite = $user->favorites()
                ->where('favoritable_type', Quote::class)
                ->where('favoritable_id', $quote->id)
                ->first();

            if ($favorite) {
                $favorite->delete();
                $isFavorited = false;
            } else {
                $user->favorites()->create([
                    'favoritable_type' => Quote::class,
                    'favoritable_id' => $quote->id,
                ]);
                $isFavorited = true;
            }
        } else {
            // Guest user - use session-based favorites
            $sessionFavorites = session()->get('favorites', []);

            if (in_array($quote->id, $sessionFavorites)) {
                // Remove from favorites
                $sessionFavorites = array_diff($sessionFavorites, [$quote->id]);
                $isFavorited = false;
            } else {
                // Add to favorites
                $sessionFavorites[] = $quote->id;
                $isFavorited = true;
            }

            session()->put('favorites', array_values($sessionFavorites));
        }

        return response()->json(['isFavorited' => $isFavorited]);
    }
}
