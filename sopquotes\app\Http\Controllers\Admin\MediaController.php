<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Media;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class MediaController extends Controller
{
    /**
     * Display a listing of media files.
     */
    public function index(Request $request)
    {
        $query = Media::with(['category', 'uploader'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                    ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('file_type')) {
            $query->where('file_type', $request->file_type);
        }

        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $media = $query->paginate(15)->withQueryString();

        return Inertia::render('admin/media/index', [
            'media' => $media,
            'categories' => Category::where('is_active', true)->get(),
            'filters' => (object) $request->only(['search', 'file_type', 'category', 'status']),
            'auth' => auth()->user(),
        ]);
    }

    /**
     * Show the form for creating a new media file.
     */
    public function create()
    {
        return Inertia::render('admin/media/create', [
            'categories' => Category::where('is_active', true)->get(),
            'auth' => auth()->user(),
        ]);
    }

    /**
     * Store a newly created media file in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category_id' => 'nullable|exists:categories,id',
            'tags' => 'nullable|string', // JSON string from frontend
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'file' => 'required|file|mimes:pdf,mp3,wav,ogg,m4a|max:51200', // 50MB max
        ]);

        // Parse tags JSON
        if ($validated['tags']) {
            $validated['tags'] = json_decode($validated['tags'], true) ?: [];
        }

        // Handle file upload
        $file = $request->file('file');
        $fileType = $this->determineFileType($file);
        $filePath = $file->store("media/{$fileType}", 'public');

        // Get file metadata
        $metadata = $this->getFileMetadata($file, $fileType);

        $media = Media::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'file_path' => $filePath,
            'file_type' => $fileType,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'metadata' => $metadata,
            'category_id' => $validated['category_id'],
            'tags' => $validated['tags'] ?? [],
            'uploaded_by' => auth()->id(),
            'is_featured' => $validated['is_featured'] ?? false,
            'is_active' => $validated['is_active'] ?? true,
        ]);

        return redirect()->route('admin.media.index')
            ->with('success', 'Media file uploaded successfully.');
    }

    /**
     * Display the specified media file.
     */
    public function show(Media $media)
    {
        $media->load(['category', 'uploader']);

        return Inertia::render('admin/media/show', [
            'media' => $media,
            'auth' => auth()->user(),
        ]);
    }

    /**
     * Show the form for editing the specified media file.
     */
    public function edit(Media $media)
    {
        $media->load(['category', 'uploader']);

        return Inertia::render('admin/media/edit', [
            'media' => $media,
            'categories' => Category::where('is_active', true)->get(),
            'auth' => auth()->user(),
        ]);
    }

    /**
     * Update the specified media file in storage.
     */
    public function update(Request $request, Media $media)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category_id' => 'nullable|exists:categories,id',
            'tags' => 'nullable|string', // JSON string from frontend
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'file' => 'nullable|file|mimes:pdf,mp3,wav,ogg,m4a|max:51200', // 50MB max
        ]);

        // Parse tags JSON
        if ($validated['tags']) {
            $validated['tags'] = json_decode($validated['tags'], true) ?: [];
        }

        $updateData = [
            'title' => $validated['title'],
            'description' => $validated['description'],
            'category_id' => $validated['category_id'],
            'tags' => $validated['tags'] ?? [],
            'is_featured' => $validated['is_featured'] ?? false,
            'is_active' => $validated['is_active'] ?? true,
        ];

        // Handle file replacement if new file uploaded
        if ($request->hasFile('file')) {
            // Delete old file
            if ($media->file_path && Storage::disk('public')->exists($media->file_path)) {
                Storage::disk('public')->delete($media->file_path);
            }

            $file = $request->file('file');
            $fileType = $this->determineFileType($file);
            $filePath = $file->store("media/{$fileType}", 'public');
            $metadata = $this->getFileMetadata($file, $fileType);

            $updateData = array_merge($updateData, [
                'file_path' => $filePath,
                'file_type' => $fileType,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'metadata' => $metadata,
            ]);
        }

        $media->update($updateData);

        return redirect()->route('admin.media.index')
            ->with('success', 'Media file updated successfully.');
    }

    /**
     * Remove the specified media file from storage.
     */
    public function destroy(Media $media)
    {
        // Delete the file from storage
        if ($media->file_path && Storage::disk('public')->exists($media->file_path)) {
            Storage::disk('public')->delete($media->file_path);
        }

        // Delete thumbnail if exists
        if ($media->thumbnail_path && Storage::disk('public')->exists($media->thumbnail_path)) {
            Storage::disk('public')->delete($media->thumbnail_path);
        }

        $media->delete();

        return redirect()->route('admin.media.index')
            ->with('success', 'Media file deleted successfully.');
    }

    /**
     * Toggle the featured status of a media file.
     */
    public function toggleFeatured(Media $media)
    {
        $media->update(['is_featured' => !$media->is_featured]);

        return back()->with('success', 'Media featured status updated.');
    }

    /**
     * Toggle the active status of a media file.
     */
    public function toggleStatus(Media $media)
    {
        $media->update(['is_active' => !$media->is_active]);

        return back()->with('success', 'Media status updated.');
    }

    /**
     * Determine file type based on MIME type.
     */
    private function determineFileType($file)
    {
        $mimeType = $file->getMimeType();

        if (str_starts_with($mimeType, 'audio/')) {
            return 'audio';
        } elseif ($mimeType === 'application/pdf') {
            return 'pdf';
        }

        return 'other';
    }

    /**
     * Get file metadata based on file type.
     */
    private function getFileMetadata($file, $fileType)
    {
        $metadata = [];

        if ($fileType === 'audio') {
            // For audio files, you could use getID3 library to extract duration, bitrate, etc.
            // For now, we'll just store basic info
            $metadata['duration'] = null; // Could be extracted with getID3
            $metadata['bitrate'] = null;
        } elseif ($fileType === 'pdf') {
            // For PDF files, you could extract page count, etc.
            $metadata['pages'] = null; // Could be extracted with PDF parser
        }

        return $metadata;
    }
}
