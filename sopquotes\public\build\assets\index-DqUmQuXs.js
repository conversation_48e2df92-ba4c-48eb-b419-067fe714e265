import{r as a,j as r,R as T,a as se,m as Oe,L as De}from"./app-BWHLaRS2.js";import{A as Le}from"./admin-layout-DKXm94gZ.js";import{c as V,B as L}from"./button-CUovRkQ3.js";import{C as z,a as Y,b as J,c as Q}from"./card-3ac6awgC.js";import{I as Z}from"./input-B4rX1XfI.js";import{L as X}from"./label-DWihlnjb.js";import{S as H,T as ee}from"./textarea-DsrDZwdm.js";import{C as $e}from"./checkbox-B6mGW3Gq.js";import"./index-CB7we0-r.js";import{c as B}from"./book-open-DxiZ0b6m.js";import{M as te}from"./mail-BHQUwL07.js";import{P as ne}from"./palette-Cq6FYu77.js";import{S as Ge}from"./settings-FGMHgWsK.js";import"./flash-message-BOB8jroJ.js";import"./sun-Bmefm2yw.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./menu-DfGfLdRU.js";import"./index-C3-VNe4Y.js";import"./index-BDjL_iy4.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ue=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],oe=B("Globe",Ue);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Be=B("RotateCcw",Ve);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],We=B("TriangleAlert",Ke);function I(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}function K(e,t=[]){let n=[];function o(c,i){const u=a.createContext(i),p=n.length;n=[...n,i];const f=m=>{var N;const{scope:h,children:g,...S}=m,b=((N=h==null?void 0:h[e])==null?void 0:N[p])||u,x=a.useMemo(()=>S,Object.values(S));return r.jsx(b.Provider,{value:x,children:g})};f.displayName=c+"Provider";function d(m,h){var b;const g=((b=h==null?void 0:h[e])==null?void 0:b[p])||u,S=a.useContext(g);if(S)return S;if(i!==void 0)return i;throw new Error(`\`${m}\` must be used within \`${c}\``)}return[f,d]}const s=()=>{const c=n.map(i=>a.createContext(i));return function(u){const p=(u==null?void 0:u[e])||c;return a.useMemo(()=>({[`__scope${e}`]:{...u,[e]:p}}),[u,p])}};return s.scopeName=e,[o,qe(s,...t)]}function qe(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(c){const i=o.reduce((u,{useScope:p,scopeName:f})=>{const m=p(c)[`__scope${f}`];return{...u,...m}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function re(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function ae(...e){return t=>{let n=!1;const o=e.map(s=>{const c=re(s,t);return!n&&typeof c=="function"&&(n=!0),c});if(n)return()=>{for(let s=0;s<o.length;s++){const c=o[s];typeof c=="function"?c():re(e[s],null)}}}}function F(...e){return a.useCallback(ae(...e),e)}function G(e){const t=ze(e),n=a.forwardRef((o,s)=>{const{children:c,...i}=o,u=a.Children.toArray(c),p=u.find(Je);if(p){const f=p.props.children,d=u.map(m=>m===p?a.Children.count(f)>1?a.Children.only(null):a.isValidElement(f)?f.props.children:null:m);return r.jsx(t,{...i,ref:s,children:a.isValidElement(f)?a.cloneElement(f,void 0,d):null})}return r.jsx(t,{...i,ref:s,children:c})});return n.displayName=`${e}.Slot`,n}function ze(e){const t=a.forwardRef((n,o)=>{const{children:s,...c}=n;if(a.isValidElement(s)){const i=Ze(s),u=Qe(c,s.props);return s.type!==a.Fragment&&(u.ref=o?ae(o,i):i),a.cloneElement(s,u)}return a.Children.count(s)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Ye=Symbol("radix.slottable");function Je(e){return a.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Ye}function Qe(e,t){const n={...t};for(const o in t){const s=e[o],c=t[o];/^on[A-Z]/.test(o)?s&&c?n[o]=(...u)=>{const p=c(...u);return s(...u),p}:s&&(n[o]=s):o==="style"?n[o]={...s,...c}:o==="className"&&(n[o]=[s,c].filter(Boolean).join(" "))}return{...e,...n}}function Ze(e){var o,s;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Xe(e){const t=e+"CollectionProvider",[n,o]=K(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=b=>{const{scope:x,children:N}=b,l=T.useRef(null),v=T.useRef(new Map).current;return r.jsx(s,{scope:x,itemMap:v,collectionRef:l,children:N})};i.displayName=t;const u=e+"CollectionSlot",p=G(u),f=T.forwardRef((b,x)=>{const{scope:N,children:l}=b,v=c(u,N),y=F(x,v.collectionRef);return r.jsx(p,{ref:y,children:l})});f.displayName=u;const d=e+"CollectionItemSlot",m="data-radix-collection-item",h=G(d),g=T.forwardRef((b,x)=>{const{scope:N,children:l,...v}=b,y=T.useRef(null),C=F(x,y),w=c(d,N);return T.useEffect(()=>(w.itemMap.set(y,{ref:y,...v}),()=>void w.itemMap.delete(y))),r.jsx(h,{[m]:"",ref:C,children:l})});g.displayName=d;function S(b){const x=c(e+"CollectionConsumer",b);return T.useCallback(()=>{const l=x.collectionRef.current;if(!l)return[];const v=Array.from(l.querySelectorAll(`[${m}]`));return Array.from(x.itemMap.values()).sort((w,M)=>v.indexOf(w.ref.current)-v.indexOf(M.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:i,Slot:f,ItemSlot:g},S,o]}var k=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},He=se[" useId ".trim().toString()]||(()=>{}),et=0;function ce(e){const[t,n]=a.useState(He());return k(()=>{n(o=>o??String(et++))},[e]),e||(t?`radix-${t}`:"")}var tt=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],A=tt.reduce((e,t)=>{const n=G(`Primitive.${t}`),o=a.forwardRef((s,c)=>{const{asChild:i,...u}=s,p=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),r.jsx(p,{...u,ref:c})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function nt(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var o;return(o=t.current)==null?void 0:o.call(t,...n)},[])}var ot=se[" useInsertionEffect ".trim().toString()]||k;function ie({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[s,c,i]=rt({defaultProp:t,onChange:n}),u=e!==void 0,p=u?e:s;{const d=a.useRef(e!==void 0);a.useEffect(()=>{const m=d.current;m!==u&&console.warn(`${o} is changing from ${m?"controlled":"uncontrolled"} to ${u?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=u},[u,o])}const f=a.useCallback(d=>{var m;if(u){const h=st(d)?d(e):d;h!==e&&((m=i.current)==null||m.call(i,h))}else c(d)},[u,e,c,i]);return[p,f]}function rt({defaultProp:e,onChange:t}){const[n,o]=a.useState(e),s=a.useRef(n),c=a.useRef(t);return ot(()=>{c.current=t},[t]),a.useEffect(()=>{var i;s.current!==n&&((i=c.current)==null||i.call(c,n),s.current=n)},[n,s]),[n,o,c]}function st(e){return typeof e=="function"}var at=a.createContext(void 0);function le(e){const t=a.useContext(at);return e||t||"ltr"}var $="rovingFocusGroup.onEntryFocus",ct={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[U,ue,it]=Xe(E),[lt,de]=K(E,[it]),[ut,dt]=lt(E),fe=a.forwardRef((e,t)=>r.jsx(U.Provider,{scope:e.__scopeRovingFocusGroup,children:r.jsx(U.Slot,{scope:e.__scopeRovingFocusGroup,children:r.jsx(ft,{...e,ref:t})})}));fe.displayName=E;var ft=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:s=!1,dir:c,currentTabStopId:i,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:p,onEntryFocus:f,preventScrollOnEntryFocus:d=!1,...m}=e,h=a.useRef(null),g=F(t,h),S=le(c),[b,x]=ie({prop:i,defaultProp:u??null,onChange:p,caller:E}),[N,l]=a.useState(!1),v=nt(f),y=ue(n),C=a.useRef(!1),[w,M]=a.useState(0);return a.useEffect(()=>{const j=h.current;if(j)return j.addEventListener($,v),()=>j.removeEventListener($,v)},[v]),r.jsx(ut,{scope:n,orientation:o,dir:S,loop:s,currentTabStopId:b,onItemFocus:a.useCallback(j=>x(j),[x]),onItemShiftTab:a.useCallback(()=>l(!0),[]),onFocusableItemAdd:a.useCallback(()=>M(j=>j+1),[]),onFocusableItemRemove:a.useCallback(()=>M(j=>j-1),[]),children:r.jsx(A.div,{tabIndex:N||w===0?-1:0,"data-orientation":o,...m,ref:g,style:{outline:"none",...e.style},onMouseDown:I(e.onMouseDown,()=>{C.current=!0}),onFocus:I(e.onFocus,j=>{const _e=!C.current;if(j.target===j.currentTarget&&_e&&!N){const q=new CustomEvent($,ct);if(j.currentTarget.dispatchEvent(q),!q.defaultPrevented){const D=y().filter(R=>R.focusable),Pe=D.find(R=>R.active),Fe=D.find(R=>R.id===b),ke=[Pe,Fe,...D].filter(Boolean).map(R=>R.ref.current);ve(ke,d)}}C.current=!1}),onBlur:I(e.onBlur,()=>l(!1))})})}),me="RovingFocusGroupItem",pe=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:s=!1,tabStopId:c,children:i,...u}=e,p=ce(),f=c||p,d=dt(me,n),m=d.currentTabStopId===f,h=ue(n),{onFocusableItemAdd:g,onFocusableItemRemove:S,currentTabStopId:b}=d;return a.useEffect(()=>{if(o)return g(),()=>S()},[o,g,S]),r.jsx(U.ItemSlot,{scope:n,id:f,focusable:o,active:s,children:r.jsx(A.span,{tabIndex:m?0:-1,"data-orientation":d.orientation,...u,ref:t,onMouseDown:I(e.onMouseDown,x=>{o?d.onItemFocus(f):x.preventDefault()}),onFocus:I(e.onFocus,()=>d.onItemFocus(f)),onKeyDown:I(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){d.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const N=vt(x,d.orientation,d.dir);if(N!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let v=h().filter(y=>y.focusable).map(y=>y.ref.current);if(N==="last")v.reverse();else if(N==="prev"||N==="next"){N==="prev"&&v.reverse();const y=v.indexOf(x.currentTarget);v=d.loop?xt(v,y+1):v.slice(y+1)}setTimeout(()=>ve(v))}}),children:typeof i=="function"?i({isCurrentTabStop:m,hasTabStop:b!=null}):i})})});pe.displayName=me;var mt={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function pt(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function vt(e,t,n){const o=pt(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return mt[o]}function ve(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function xt(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var ht=fe,yt=pe;function gt(e,t){return a.useReducer((n,o)=>t[n][o]??n,e)}var xe=e=>{const{present:t,children:n}=e,o=bt(t),s=typeof n=="function"?n({present:o.isPresent}):a.Children.only(n),c=F(o.ref,Ct(s));return typeof n=="function"||o.isPresent?a.cloneElement(s,{ref:c}):null};xe.displayName="Presence";function bt(e){const[t,n]=a.useState(),o=a.useRef(null),s=a.useRef(e),c=a.useRef("none"),i=e?"mounted":"unmounted",[u,p]=gt(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const f=_(o.current);c.current=u==="mounted"?f:"none"},[u]),k(()=>{const f=o.current,d=s.current;if(d!==e){const h=c.current,g=_(f);e?p("MOUNT"):g==="none"||(f==null?void 0:f.display)==="none"?p("UNMOUNT"):p(d&&h!==g?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,p]),k(()=>{if(t){let f;const d=t.ownerDocument.defaultView??window,m=g=>{const b=_(o.current).includes(CSS.escape(g.animationName));if(g.target===t&&b&&(p("ANIMATION_END"),!s.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",f=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},h=g=>{g.target===t&&(c.current=_(o.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{d.clearTimeout(f),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else p("ANIMATION_END")},[t,p]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(f=>{o.current=f?getComputedStyle(f):null,n(f)},[])}}function _(e){return(e==null?void 0:e.animationName)||"none"}function Ct(e){var o,s;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var O="Tabs",[Nt,zt]=K(O,[de]),he=de(),[St,W]=Nt(O),ye=a.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,onValueChange:s,defaultValue:c,orientation:i="horizontal",dir:u,activationMode:p="automatic",...f}=e,d=le(u),[m,h]=ie({prop:o,onChange:s,defaultProp:c??"",caller:O});return r.jsx(St,{scope:n,baseId:ce(),value:m,onValueChange:h,orientation:i,dir:d,activationMode:p,children:r.jsx(A.div,{dir:d,"data-orientation":i,...f,ref:t})})});ye.displayName=O;var ge="TabsList",be=a.forwardRef((e,t)=>{const{__scopeTabs:n,loop:o=!0,...s}=e,c=W(ge,n),i=he(n);return r.jsx(ht,{asChild:!0,...i,orientation:c.orientation,dir:c.dir,loop:o,children:r.jsx(A.div,{role:"tablist","aria-orientation":c.orientation,...s,ref:t})})});be.displayName=ge;var Ce="TabsTrigger",Ne=a.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,disabled:s=!1,...c}=e,i=W(Ce,n),u=he(n),p=we(i.baseId,o),f=Ie(i.baseId,o),d=o===i.value;return r.jsx(yt,{asChild:!0,...u,focusable:!s,active:d,children:r.jsx(A.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":f,"data-state":d?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:p,...c,ref:t,onMouseDown:I(e.onMouseDown,m=>{!s&&m.button===0&&m.ctrlKey===!1?i.onValueChange(o):m.preventDefault()}),onKeyDown:I(e.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&i.onValueChange(o)}),onFocus:I(e.onFocus,()=>{const m=i.activationMode!=="manual";!d&&!s&&m&&i.onValueChange(o)})})})});Ne.displayName=Ce;var Se="TabsContent",je=a.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,forceMount:s,children:c,...i}=e,u=W(Se,n),p=we(u.baseId,o),f=Ie(u.baseId,o),d=o===u.value,m=a.useRef(d);return a.useEffect(()=>{const h=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(h)},[]),r.jsx(xe,{present:s||d,children:({present:h})=>r.jsx(A.div,{"data-state":d?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":p,hidden:!h,id:f,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:h&&c})})});je.displayName=Se;function we(e,t){return`${e}-trigger-${t}`}function Ie(e,t){return`${e}-content-${t}`}var jt=ye,Re=be,Te=Ne,Ae=je;const wt=jt,Ee=a.forwardRef(({className:e,...t},n)=>r.jsx(Re,{ref:n,className:V("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Ee.displayName=Re.displayName;const P=a.forwardRef(({className:e,...t},n)=>r.jsx(Te,{ref:n,className:V("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));P.displayName=Te.displayName;const Me=a.forwardRef(({className:e,...t},n)=>r.jsx(Ae,{ref:n,className:V("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Me.displayName=Ae.displayName;function Yt({settings:e,auth:t}){const[n,o]=a.useState("general"),s=Object.entries(e).flatMap(([l,v])=>v.map(y=>({...y,group:l}))),{data:c,setData:i,patch:u,processing:p,errors:f,reset:d}=Oe({settings:s}),m=l=>{l.preventDefault(),u(route("admin.settings.update"))},h=l=>{confirm(`Are you sure you want to reset all ${l} settings to defaults? This action cannot be undone.`)&&u(route("admin.settings.reset"),{data:{group:l},preserveScroll:!0})},g=(l,v,y)=>{const C=c.settings.map(w=>w.key===l?{...w,[v]:y}:w);i("settings",C)},S=l=>c.settings.filter(v=>v.group===l),b=l=>{const v={id:l.key,className:f[`settings.${l.key}`]?"border-red-500":""};switch(l.type){case"boolean":return r.jsx($e,{...v,checked:l.value==="1"||l.value===!0,onCheckedChange:y=>g(l.key,"value",y?"1":"0")});case"integer":return r.jsx(Z,{...v,type:"number",value:l.value||"",onChange:y=>g(l.key,"value",y.target.value)});case"json":return r.jsx(ee,{...v,value:typeof l.value=="object"?JSON.stringify(l.value,null,2):l.value||"",onChange:y=>g(l.key,"value",y.target.value),rows:4});default:return l.key.includes("description")||l.key.includes("content")?r.jsx(ee,{...v,value:l.value||"",onChange:y=>g(l.key,"value",y.target.value),rows:3}):r.jsx(Z,{...v,type:l.key.includes("email")?"email":l.key.includes("time")?"time":"text",value:l.value||"",onChange:y=>g(l.key,"value",y.target.value)})}},x=l=>{switch(l){case"general":return oe;case"email":return te;case"appearance":return ne;default:return Ge}},N=l=>l.charAt(0).toUpperCase()+l.slice(1)+" Settings";return r.jsxs(Le,{children:[r.jsx(De,{title:"System Settings - Admin Dashboard"}),r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"System Settings"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Configure your SOP Quotes application settings"})]}),r.jsxs("form",{onSubmit:m,children:[r.jsxs(wt,{value:n,onValueChange:o,children:[r.jsxs(Ee,{className:"grid w-full grid-cols-3",children:[r.jsxs(P,{value:"general",className:"flex items-center space-x-2",children:[r.jsx(oe,{className:"h-4 w-4"}),r.jsx("span",{children:"General"})]}),r.jsxs(P,{value:"email",className:"flex items-center space-x-2",children:[r.jsx(te,{className:"h-4 w-4"}),r.jsx("span",{children:"Email"})]}),r.jsxs(P,{value:"appearance",className:"flex items-center space-x-2",children:[r.jsx(ne,{className:"h-4 w-4"}),r.jsx("span",{children:"Appearance"})]})]}),Object.keys(e).map(l=>{const v=x(l),y=S(l);return r.jsx(Me,{value:l,className:"space-y-6",children:r.jsxs(z,{children:[r.jsxs(Y,{className:"flex flex-row items-center justify-between",children:[r.jsxs(J,{className:"flex items-center space-x-2",children:[r.jsx(v,{className:"h-5 w-5"}),r.jsx("span",{children:N(l)})]}),r.jsxs(L,{type:"button",variant:"outline",size:"sm",onClick:()=>h(l),className:"text-red-600 hover:text-red-700",children:[r.jsx(Be,{className:"h-4 w-4 mr-2"}),"Reset to Defaults"]})]}),r.jsx(Q,{className:"space-y-6",children:y.map(C=>r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx(X,{htmlFor:C.key,className:"text-sm font-medium",children:C.key.replace(/_/g," ").replace(/\b\w/g,w=>w.toUpperCase())}),C.is_public&&r.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded",children:"Public"})]}),C.type==="boolean"?r.jsxs("div",{className:"flex items-center space-x-2",children:[b(C),r.jsx(X,{htmlFor:C.key,className:"text-sm text-gray-600",children:C.description})]}):r.jsxs(r.Fragment,{children:[b(C),C.description&&r.jsx("p",{className:"text-sm text-gray-500",children:C.description})]}),f[`settings.${C.key}`]&&r.jsx("p",{className:"text-red-500 text-sm",children:f[`settings.${C.key}`]})]},C.key))})]})},l)})]}),r.jsxs("div",{className:"flex justify-end space-x-4 mt-6",children:[r.jsx(L,{type:"button",variant:"outline",onClick:()=>d(),children:"Reset Changes"}),r.jsx(L,{type:"submit",disabled:p,children:p?r.jsxs(r.Fragment,{children:[r.jsx(H,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):r.jsxs(r.Fragment,{children:[r.jsx(H,{className:"h-4 w-4 mr-2"}),"Save Settings"]})})]})]}),r.jsxs(z,{className:"border-yellow-200 dark:border-yellow-800",children:[r.jsx(Y,{children:r.jsxs(J,{className:"flex items-center space-x-2 text-yellow-600 dark:text-yellow-400",children:[r.jsx(We,{className:"h-5 w-5"}),r.jsx("span",{children:"Important Notes"})]})}),r.jsxs(Q,{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[r.jsx("p",{children:'• Settings marked as "Public" are accessible to the frontend and will be cached for performance.'}),r.jsx("p",{children:"• Email settings require proper SMTP configuration in your environment file."}),r.jsx("p",{children:"• Changes to appearance settings may require users to refresh their browsers."}),r.jsx("p",{children:"• Resetting settings to defaults will permanently delete current values."})]})]})]})]})}export{Yt as default};
