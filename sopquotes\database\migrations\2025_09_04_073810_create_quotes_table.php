<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotes', function (Blueprint $table) {
            $table->id();
            $table->text('text'); // The quote text
            $table->string('source')->nullable(); // Bible reference, book title, etc.
            $table->string('author')->nullable(); // Author name
            $table->string('reference')->nullable(); // Specific verse reference
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->json('tags')->nullable(); // Array of tags for categorization
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->boolean('is_featured')->default(false); // For daily quotes
            $table->boolean('is_active')->default(true); // For soft hiding
            $table->timestamps();

            // Indexes for better performance
            $table->index(['is_active', 'is_featured']);
            $table->index(['category_id', 'is_active']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotes');
    }
};
