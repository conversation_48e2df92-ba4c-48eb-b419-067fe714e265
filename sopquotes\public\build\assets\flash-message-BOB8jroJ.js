import{r as a,j as w,a as ot,R as fe,K as to}from"./app-BWHLaRS2.js";import{r as En,R as no}from"./index-CB7we0-r.js";import{c as it}from"./button-CUovRkQ3.js";import{c as De}from"./book-open-DxiZ0b6m.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ro=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],oo=De("CircleAlert",ro);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const io=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],so=De("CircleCheckBig",io);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const co=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],ao=De("CircleX",co);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uo=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],Zt=De("Info",uo);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lo=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],fo=De("X",lo);function qe(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Qt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Sn(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Qt(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Qt(e[o],null)}}}}function po(e,t=[]){let n=[];function r(i,s){const c=a.createContext(s),d=n.length;n=[...n,s];const l=u=>{var y;const{scope:p,children:h,...g}=u,m=((y=p==null?void 0:p[e])==null?void 0:y[d])||c,v=a.useMemo(()=>g,Object.values(g));return w.jsx(m.Provider,{value:v,children:h})};l.displayName=i+"Provider";function f(u,p){var m;const h=((m=p==null?void 0:p[e])==null?void 0:m[d])||c,g=a.useContext(h);if(g)return g;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[l,f]}const o=()=>{const i=n.map(s=>a.createContext(s));return function(c){const d=(c==null?void 0:c[e])||i;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:d}}),[c,d])}};return o.scopeName=e,[r,mo(o,...t)]}function mo(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((c,{useScope:d,scopeName:l})=>{const u=d(i)[`__scope${l}`];return{...c,...u}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Rn=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},ho=ot[" useInsertionEffect ".trim().toString()]||Rn;function vo({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=go({defaultProp:t,onChange:n}),c=e!==void 0,d=c?e:o;{const f=a.useRef(e!==void 0);a.useEffect(()=>{const u=f.current;u!==c&&console.warn(`${r} is changing from ${u?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=c},[c,r])}const l=a.useCallback(f=>{var u;if(c){const p=wo(f)?f(e):f;p!==e&&((u=s.current)==null||u.call(s,p))}else i(f)},[c,e,i,s]);return[d,l]}function go({defaultProp:e,onChange:t}){const[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return ho(()=>{i.current=t},[t]),a.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function wo(e){return typeof e=="function"}function yo(e){const t=xo(e),n=a.forwardRef((r,o)=>{const{children:i,...s}=r,c=a.Children.toArray(i),d=c.find(Co);if(d){const l=d.props.children,f=c.map(u=>u===d?a.Children.count(l)>1?a.Children.only(null):a.isValidElement(l)?l.props.children:null:u);return w.jsx(t,{...s,ref:o,children:a.isValidElement(l)?a.cloneElement(l,void 0,f):null})}return w.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}function xo(e){const t=a.forwardRef((n,r)=>{const{children:o,...i}=n;if(a.isValidElement(o)){const s=So(o),c=Eo(i,o.props);return o.type!==a.Fragment&&(c.ref=r?Sn(r,s):s),a.cloneElement(o,c)}return a.Children.count(o)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var bo=Symbol("radix.slottable");function Co(e){return a.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===bo}function Eo(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...c)=>{const d=i(...c);return o(...c),d}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function So(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ro=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Mo=Ro.reduce((e,t)=>{const n=yo(`Primitive.${t}`),r=a.forwardRef((o,i)=>{const{asChild:s,...c}=o,d=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),w.jsx(d,{...c,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function O(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function st(e,t=[]){let n=[];function r(i,s){const c=a.createContext(s),d=n.length;n=[...n,s];const l=u=>{var y;const{scope:p,children:h,...g}=u,m=((y=p==null?void 0:p[e])==null?void 0:y[d])||c,v=a.useMemo(()=>g,Object.values(g));return w.jsx(m.Provider,{value:v,children:h})};l.displayName=i+"Provider";function f(u,p){var m;const h=((m=p==null?void 0:p[e])==null?void 0:m[d])||c,g=a.useContext(h);if(g)return g;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[l,f]}const o=()=>{const i=n.map(s=>a.createContext(s));return function(c){const d=(c==null?void 0:c[e])||i;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:d}}),[c,d])}};return o.scopeName=e,[r,Po(o,...t)]}function Po(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((c,{useScope:d,scopeName:l})=>{const u=d(i)[`__scope${l}`];return{...c,...u}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Jt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Nt(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Jt(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Jt(e[o],null)}}}}function U(...e){return a.useCallback(Nt(...e),e)}function Ze(e){const t=Ao(e),n=a.forwardRef((r,o)=>{const{children:i,...s}=r,c=a.Children.toArray(i),d=c.find(_o);if(d){const l=d.props.children,f=c.map(u=>u===d?a.Children.count(l)>1?a.Children.only(null):a.isValidElement(l)?l.props.children:null:u);return w.jsx(t,{...s,ref:o,children:a.isValidElement(l)?a.cloneElement(l,void 0,f):null})}return w.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}function Ao(e){const t=a.forwardRef((n,r)=>{const{children:o,...i}=n;if(a.isValidElement(o)){const s=Io(o),c=No(i,o.props);return o.type!==a.Fragment&&(c.ref=r?Nt(r,s):s),a.cloneElement(o,c)}return a.Children.count(o)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Oo=Symbol("radix.slottable");function _o(e){return a.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Oo}function No(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...c)=>{const d=i(...c);return o(...c),d}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Io(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Mn(e){const t=e+"CollectionProvider",[n,r]=st(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:v,children:y}=m,C=fe.useRef(null),x=fe.useRef(new Map).current;return w.jsx(o,{scope:v,itemMap:x,collectionRef:C,children:y})};s.displayName=t;const c=e+"CollectionSlot",d=Ze(c),l=fe.forwardRef((m,v)=>{const{scope:y,children:C}=m,x=i(c,y),b=U(v,x.collectionRef);return w.jsx(d,{ref:b,children:C})});l.displayName=c;const f=e+"CollectionItemSlot",u="data-radix-collection-item",p=Ze(f),h=fe.forwardRef((m,v)=>{const{scope:y,children:C,...x}=m,b=fe.useRef(null),E=U(v,b),M=i(f,y);return fe.useEffect(()=>(M.itemMap.set(b,{ref:b,...x}),()=>void M.itemMap.delete(b))),w.jsx(p,{[u]:"",ref:E,children:C})});h.displayName=f;function g(m){const v=i(e+"CollectionConsumer",m);return fe.useCallback(()=>{const C=v.collectionRef.current;if(!C)return[];const x=Array.from(C.querySelectorAll(`[${u}]`));return Array.from(v.itemMap.values()).sort((M,S)=>x.indexOf(M.ref.current)-x.indexOf(S.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:s,Slot:l,ItemSlot:h},g,r]}var Do=a.createContext(void 0);function Pn(e){const t=a.useContext(Do);return e||t||"ltr"}var To=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],K=To.reduce((e,t)=>{const n=Ze(`Primitive.${t}`),r=a.forwardRef((o,i)=>{const{asChild:s,...c}=o,d=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),w.jsx(d,{...c,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function An(e,t){e&&En.flushSync(()=>e.dispatchEvent(t))}function ne(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function ko(e,t=globalThis==null?void 0:globalThis.document){const n=ne(e);a.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Lo="DismissableLayer",St="dismissableLayer.update",Fo="dismissableLayer.pointerDownOutside",jo="dismissableLayer.focusOutside",en,On=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),_n=a.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:c,...d}=e,l=a.useContext(On),[f,u]=a.useState(null),p=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=a.useState({}),g=U(t,S=>u(S)),m=Array.from(l.layers),[v]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),y=m.indexOf(v),C=f?m.indexOf(f):-1,x=l.layersWithOutsidePointerEventsDisabled.size>0,b=C>=y,E=Wo(S=>{const P=S.target,I=[...l.branches].some(_=>_.contains(P));!b||I||(o==null||o(S),s==null||s(S),S.defaultPrevented||c==null||c())},p),M=Uo(S=>{const P=S.target;[...l.branches].some(_=>_.contains(P))||(i==null||i(S),s==null||s(S),S.defaultPrevented||c==null||c())},p);return ko(S=>{C===l.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&c&&(S.preventDefault(),c()))},p),a.useEffect(()=>{if(f)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(en=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(f)),l.layers.add(f),tn(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=en)}},[f,p,n,l]),a.useEffect(()=>()=>{f&&(l.layers.delete(f),l.layersWithOutsidePointerEventsDisabled.delete(f),tn())},[f,l]),a.useEffect(()=>{const S=()=>h({});return document.addEventListener(St,S),()=>document.removeEventListener(St,S)},[]),w.jsx(K.div,{...d,ref:g,style:{pointerEvents:x?b?"auto":"none":void 0,...e.style},onFocusCapture:O(e.onFocusCapture,M.onFocusCapture),onBlurCapture:O(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:O(e.onPointerDownCapture,E.onPointerDownCapture)})});_n.displayName=Lo;var $o="DismissableLayerBranch",Bo=a.forwardRef((e,t)=>{const n=a.useContext(On),r=a.useRef(null),o=U(t,r);return a.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),w.jsx(K.div,{...e,ref:o})});Bo.displayName=$o;function Wo(e,t=globalThis==null?void 0:globalThis.document){const n=ne(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{const i=c=>{if(c.target&&!r.current){let d=function(){Nn(Fo,n,l,{discrete:!0})};const l={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=d,t.addEventListener("click",o.current,{once:!0})):d()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Uo(e,t=globalThis==null?void 0:globalThis.document){const n=ne(e),r=a.useRef(!1);return a.useEffect(()=>{const o=i=>{i.target&&!r.current&&Nn(jo,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function tn(){const e=new CustomEvent(St);document.dispatchEvent(e)}function Nn(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?An(o,i):o.dispatchEvent(i)}var mt=0;function Ko(){a.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??nn()),document.body.insertAdjacentElement("beforeend",e[1]??nn()),mt++,()=>{mt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),mt--}},[])}function nn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ht="focusScope.autoFocusOnMount",vt="focusScope.autoFocusOnUnmount",rn={bubbles:!1,cancelable:!0},Vo="FocusScope",In=a.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[c,d]=a.useState(null),l=ne(o),f=ne(i),u=a.useRef(null),p=U(t,m=>d(m)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let m=function(x){if(h.paused||!c)return;const b=x.target;c.contains(b)?u.current=b:ie(u.current,{select:!0})},v=function(x){if(h.paused||!c)return;const b=x.relatedTarget;b!==null&&(c.contains(b)||ie(u.current,{select:!0}))},y=function(x){if(document.activeElement===document.body)for(const E of x)E.removedNodes.length>0&&ie(c)};document.addEventListener("focusin",m),document.addEventListener("focusout",v);const C=new MutationObserver(y);return c&&C.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",v),C.disconnect()}}},[r,c,h.paused]),a.useEffect(()=>{if(c){sn.add(h);const m=document.activeElement;if(!c.contains(m)){const y=new CustomEvent(ht,rn);c.addEventListener(ht,l),c.dispatchEvent(y),y.defaultPrevented||(Go(qo(Dn(c)),{select:!0}),document.activeElement===m&&ie(c))}return()=>{c.removeEventListener(ht,l),setTimeout(()=>{const y=new CustomEvent(vt,rn);c.addEventListener(vt,f),c.dispatchEvent(y),y.defaultPrevented||ie(m??document.body,{select:!0}),c.removeEventListener(vt,f),sn.remove(h)},0)}}},[c,l,f,h]);const g=a.useCallback(m=>{if(!n&&!r||h.paused)return;const v=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,y=document.activeElement;if(v&&y){const C=m.currentTarget,[x,b]=zo(C);x&&b?!m.shiftKey&&y===b?(m.preventDefault(),n&&ie(x,{select:!0})):m.shiftKey&&y===x&&(m.preventDefault(),n&&ie(b,{select:!0})):y===C&&m.preventDefault()}},[n,r,h.paused]);return w.jsx(K.div,{tabIndex:-1,...s,ref:p,onKeyDown:g})});In.displayName=Vo;function Go(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ie(r,{select:t}),document.activeElement!==n)return}function zo(e){const t=Dn(e),n=on(t,e),r=on(t.reverse(),e);return[n,r]}function Dn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function on(e,t){for(const n of e)if(!Ho(n,{upTo:t}))return n}function Ho(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Yo(e){return e instanceof HTMLInputElement&&"select"in e}function ie(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Yo(e)&&t&&e.select()}}var sn=Xo();function Xo(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=cn(e,t),e.unshift(t)},remove(t){var n;e=cn(e,t),(n=e[0])==null||n.resume()}}}function cn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function qo(e){return e.filter(t=>t.tagName!=="A")}var se=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},Zo=ot[" useId ".trim().toString()]||(()=>{}),Qo=0;function Jo(e){const[t,n]=a.useState(Zo());return se(()=>{n(r=>r??String(Qo++))},[e]),t?`radix-${t}`:""}const ei=["top","right","bottom","left"],ce=Math.min,V=Math.max,Qe=Math.round,We=Math.floor,Q=e=>({x:e,y:e}),ti={left:"right",right:"left",bottom:"top",top:"bottom"},ni={start:"end",end:"start"};function Rt(e,t,n){return V(e,ce(t,n))}function re(e,t){return typeof e=="function"?e(t):e}function oe(e){return e.split("-")[0]}function be(e){return e.split("-")[1]}function It(e){return e==="x"?"y":"x"}function Dt(e){return e==="y"?"height":"width"}function ae(e){return["top","bottom"].includes(oe(e))?"y":"x"}function Tt(e){return It(ae(e))}function ri(e,t,n){n===void 0&&(n=!1);const r=be(e),o=Tt(e),i=Dt(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Je(s)),[s,Je(s)]}function oi(e){const t=Je(e);return[Mt(e),t,Mt(t)]}function Mt(e){return e.replace(/start|end/g,t=>ni[t])}function ii(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function si(e,t,n,r){const o=be(e);let i=ii(oe(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Mt)))),i}function Je(e){return e.replace(/left|right|bottom|top/g,t=>ti[t])}function ci(e){return{top:0,right:0,bottom:0,left:0,...e}}function Tn(e){return typeof e!="number"?ci(e):{top:e,right:e,bottom:e,left:e}}function et(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function an(e,t,n){let{reference:r,floating:o}=e;const i=ae(t),s=Tt(t),c=Dt(s),d=oe(t),l=i==="y",f=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,p=r[c]/2-o[c]/2;let h;switch(d){case"top":h={x:f,y:r.y-o.height};break;case"bottom":h={x:f,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:u};break;case"left":h={x:r.x-o.width,y:u};break;default:h={x:r.x,y:r.y}}switch(be(t)){case"start":h[s]-=p*(n&&l?-1:1);break;case"end":h[s]+=p*(n&&l?-1:1);break}return h}const ai=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,c=i.filter(Boolean),d=await(s.isRTL==null?void 0:s.isRTL(t));let l=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:u}=an(l,r,d),p=r,h={},g=0;for(let m=0;m<c.length;m++){const{name:v,fn:y}=c[m],{x:C,y:x,data:b,reset:E}=await y({x:f,y:u,initialPlacement:r,placement:p,strategy:o,middlewareData:h,rects:l,platform:s,elements:{reference:e,floating:t}});f=C??f,u=x??u,h={...h,[v]:{...h[v],...b}},E&&g<=50&&(g++,typeof E=="object"&&(E.placement&&(p=E.placement),E.rects&&(l=E.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:f,y:u}=an(l,p,d)),m=-1)}return{x:f,y:u,placement:p,strategy:o,middlewareData:h}};async function Oe(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:c,strategy:d}=e,{boundary:l="clippingAncestors",rootBoundary:f="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=re(t,e),g=Tn(h),v=c[p?u==="floating"?"reference":"floating":u],y=et(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(c.floating)),boundary:l,rootBoundary:f,strategy:d})),C=u==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,x=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c.floating)),b=await(i.isElement==null?void 0:i.isElement(x))?await(i.getScale==null?void 0:i.getScale(x))||{x:1,y:1}:{x:1,y:1},E=et(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:C,offsetParent:x,strategy:d}):C);return{top:(y.top-E.top+g.top)/b.y,bottom:(E.bottom-y.bottom+g.bottom)/b.y,left:(y.left-E.left+g.left)/b.x,right:(E.right-y.right+g.right)/b.x}}const ui=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:c,middlewareData:d}=t,{element:l,padding:f=0}=re(e,t)||{};if(l==null)return{};const u=Tn(f),p={x:n,y:r},h=Tt(o),g=Dt(h),m=await s.getDimensions(l),v=h==="y",y=v?"top":"left",C=v?"bottom":"right",x=v?"clientHeight":"clientWidth",b=i.reference[g]+i.reference[h]-p[h]-i.floating[g],E=p[h]-i.reference[h],M=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let S=M?M[x]:0;(!S||!await(s.isElement==null?void 0:s.isElement(M)))&&(S=c.floating[x]||i.floating[g]);const P=b/2-E/2,I=S/2-m[g]/2-1,_=ce(u[y],I),L=ce(u[C],I),F=_,T=S-m[g]-L,D=S/2-m[g]/2+P,$=Rt(F,D,T),N=!d.arrow&&be(o)!=null&&D!==$&&i.reference[g]/2-(D<F?_:L)-m[g]/2<0,j=N?D<F?D-F:D-T:0;return{[h]:p[h]+j,data:{[h]:$,centerOffset:D-$-j,...N&&{alignmentOffset:j}},reset:N}}}),li=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:c,platform:d,elements:l}=t,{mainAxis:f=!0,crossAxis:u=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:m=!0,...v}=re(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const y=oe(o),C=ae(c),x=oe(c)===c,b=await(d.isRTL==null?void 0:d.isRTL(l.floating)),E=p||(x||!m?[Je(c)]:oi(c)),M=g!=="none";!p&&M&&E.push(...si(c,m,g,b));const S=[c,...E],P=await Oe(t,v),I=[];let _=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&I.push(P[y]),u){const D=ri(o,s,b);I.push(P[D[0]],P[D[1]])}if(_=[..._,{placement:o,overflows:I}],!I.every(D=>D<=0)){var L,F;const D=(((L=i.flip)==null?void 0:L.index)||0)+1,$=S[D];if($)return{data:{index:D,overflows:_},reset:{placement:$}};let N=(F=_.filter(j=>j.overflows[0]<=0).sort((j,A)=>j.overflows[1]-A.overflows[1])[0])==null?void 0:F.placement;if(!N)switch(h){case"bestFit":{var T;const j=(T=_.filter(A=>{if(M){const R=ae(A.placement);return R===C||R==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(R=>R>0).reduce((R,k)=>R+k,0)]).sort((A,R)=>A[1]-R[1])[0])==null?void 0:T[0];j&&(N=j);break}case"initialPlacement":N=c;break}if(o!==N)return{reset:{placement:N}}}return{}}}};function un(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ln(e){return ei.some(t=>e[t]>=0)}const fi=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=re(e,t);switch(r){case"referenceHidden":{const i=await Oe(t,{...o,elementContext:"reference"}),s=un(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:ln(s)}}}case"escaped":{const i=await Oe(t,{...o,altBoundary:!0}),s=un(i,n.floating);return{data:{escapedOffsets:s,escaped:ln(s)}}}default:return{}}}}};async function di(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=oe(n),c=be(n),d=ae(n)==="y",l=["left","top"].includes(s)?-1:1,f=i&&d?-1:1,u=re(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:g}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof g=="number"&&(h=c==="end"?g*-1:g),d?{x:h*f,y:p*l}:{x:p*l,y:h*f}}const pi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:c}=t,d=await di(t,e);return s===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+d.x,y:i+d.y,data:{...d,placement:s}}}}},mi=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:c={fn:v=>{let{x:y,y:C}=v;return{x:y,y:C}}},...d}=re(e,t),l={x:n,y:r},f=await Oe(t,d),u=ae(oe(o)),p=It(u);let h=l[p],g=l[u];if(i){const v=p==="y"?"top":"left",y=p==="y"?"bottom":"right",C=h+f[v],x=h-f[y];h=Rt(C,h,x)}if(s){const v=u==="y"?"top":"left",y=u==="y"?"bottom":"right",C=g+f[v],x=g-f[y];g=Rt(C,g,x)}const m=c.fn({...t,[p]:h,[u]:g});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[p]:i,[u]:s}}}}}},hi=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:c=0,mainAxis:d=!0,crossAxis:l=!0}=re(e,t),f={x:n,y:r},u=ae(o),p=It(u);let h=f[p],g=f[u];const m=re(c,t),v=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(d){const x=p==="y"?"height":"width",b=i.reference[p]-i.floating[x]+v.mainAxis,E=i.reference[p]+i.reference[x]-v.mainAxis;h<b?h=b:h>E&&(h=E)}if(l){var y,C;const x=p==="y"?"width":"height",b=["top","left"].includes(oe(o)),E=i.reference[u]-i.floating[x]+(b&&((y=s.offset)==null?void 0:y[u])||0)+(b?0:v.crossAxis),M=i.reference[u]+i.reference[x]+(b?0:((C=s.offset)==null?void 0:C[u])||0)-(b?v.crossAxis:0);g<E?g=E:g>M&&(g=M)}return{[p]:h,[u]:g}}}},vi=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:c}=t,{apply:d=()=>{},...l}=re(e,t),f=await Oe(t,l),u=oe(o),p=be(o),h=ae(o)==="y",{width:g,height:m}=i.floating;let v,y;u==="top"||u==="bottom"?(v=u,y=p===(await(s.isRTL==null?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(y=u,v=p==="end"?"top":"bottom");const C=m-f.top-f.bottom,x=g-f.left-f.right,b=ce(m-f[v],C),E=ce(g-f[y],x),M=!t.middlewareData.shift;let S=b,P=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=x),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=C),M&&!p){const _=V(f.left,0),L=V(f.right,0),F=V(f.top,0),T=V(f.bottom,0);h?P=g-2*(_!==0||L!==0?_+L:V(f.left,f.right)):S=m-2*(F!==0||T!==0?F+T:V(f.top,f.bottom))}await d({...t,availableWidth:P,availableHeight:S});const I=await s.getDimensions(c.floating);return g!==I.width||m!==I.height?{reset:{rects:!0}}:{}}}};function ct(){return typeof window<"u"}function Ce(e){return kn(e)?(e.nodeName||"").toLowerCase():"#document"}function G(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ee(e){var t;return(t=(kn(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function kn(e){return ct()?e instanceof Node||e instanceof G(e).Node:!1}function Y(e){return ct()?e instanceof Element||e instanceof G(e).Element:!1}function J(e){return ct()?e instanceof HTMLElement||e instanceof G(e).HTMLElement:!1}function fn(e){return!ct()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof G(e).ShadowRoot}function Te(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=X(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function gi(e){return["table","td","th"].includes(Ce(e))}function at(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function kt(e){const t=Lt(),n=Y(e)?X(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function wi(e){let t=ue(e);for(;J(t)&&!xe(t);){if(kt(t))return t;if(at(t))return null;t=ue(t)}return null}function Lt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function xe(e){return["html","body","#document"].includes(Ce(e))}function X(e){return G(e).getComputedStyle(e)}function ut(e){return Y(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ue(e){if(Ce(e)==="html")return e;const t=e.assignedSlot||e.parentNode||fn(e)&&e.host||ee(e);return fn(t)?t.host:t}function Ln(e){const t=ue(e);return xe(t)?e.ownerDocument?e.ownerDocument.body:e.body:J(t)&&Te(t)?t:Ln(t)}function _e(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Ln(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=G(o);if(i){const c=Pt(s);return t.concat(s,s.visualViewport||[],Te(o)?o:[],c&&n?_e(c):[])}return t.concat(o,_e(o,[],n))}function Pt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Fn(e){const t=X(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=J(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,c=Qe(n)!==i||Qe(r)!==s;return c&&(n=i,r=s),{width:n,height:r,$:c}}function Ft(e){return Y(e)?e:e.contextElement}function we(e){const t=Ft(e);if(!J(t))return Q(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Fn(t);let s=(i?Qe(n.width):n.width)/r,c=(i?Qe(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const yi=Q(0);function jn(e){const t=G(e);return!Lt()||!t.visualViewport?yi:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function xi(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==G(e)?!1:t}function de(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Ft(e);let s=Q(1);t&&(r?Y(r)&&(s=we(r)):s=we(e));const c=xi(i,n,r)?jn(i):Q(0);let d=(o.left+c.x)/s.x,l=(o.top+c.y)/s.y,f=o.width/s.x,u=o.height/s.y;if(i){const p=G(i),h=r&&Y(r)?G(r):r;let g=p,m=Pt(g);for(;m&&r&&h!==g;){const v=we(m),y=m.getBoundingClientRect(),C=X(m),x=y.left+(m.clientLeft+parseFloat(C.paddingLeft))*v.x,b=y.top+(m.clientTop+parseFloat(C.paddingTop))*v.y;d*=v.x,l*=v.y,f*=v.x,u*=v.y,d+=x,l+=b,g=G(m),m=Pt(g)}}return et({width:f,height:u,x:d,y:l})}function jt(e,t){const n=ut(e).scrollLeft;return t?t.left+n:de(ee(e)).left+n}function $n(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:jt(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function bi(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=ee(r),c=t?at(t.floating):!1;if(r===s||c&&i)return n;let d={scrollLeft:0,scrollTop:0},l=Q(1);const f=Q(0),u=J(r);if((u||!u&&!i)&&((Ce(r)!=="body"||Te(s))&&(d=ut(r)),J(r))){const h=de(r);l=we(r),f.x=h.x+r.clientLeft,f.y=h.y+r.clientTop}const p=s&&!u&&!i?$n(s,d,!0):Q(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-d.scrollLeft*l.x+f.x+p.x,y:n.y*l.y-d.scrollTop*l.y+f.y+p.y}}function Ci(e){return Array.from(e.getClientRects())}function Ei(e){const t=ee(e),n=ut(e),r=e.ownerDocument.body,o=V(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=V(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+jt(e);const c=-n.scrollTop;return X(r).direction==="rtl"&&(s+=V(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:c}}function Si(e,t){const n=G(e),r=ee(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,c=0,d=0;if(o){i=o.width,s=o.height;const l=Lt();(!l||l&&t==="fixed")&&(c=o.offsetLeft,d=o.offsetTop)}return{width:i,height:s,x:c,y:d}}function Ri(e,t){const n=de(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=J(e)?we(e):Q(1),s=e.clientWidth*i.x,c=e.clientHeight*i.y,d=o*i.x,l=r*i.y;return{width:s,height:c,x:d,y:l}}function dn(e,t,n){let r;if(t==="viewport")r=Si(e,n);else if(t==="document")r=Ei(ee(e));else if(Y(t))r=Ri(t,n);else{const o=jn(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return et(r)}function Bn(e,t){const n=ue(e);return n===t||!Y(n)||xe(n)?!1:X(n).position==="fixed"||Bn(n,t)}function Mi(e,t){const n=t.get(e);if(n)return n;let r=_e(e,[],!1).filter(c=>Y(c)&&Ce(c)!=="body"),o=null;const i=X(e).position==="fixed";let s=i?ue(e):e;for(;Y(s)&&!xe(s);){const c=X(s),d=kt(s);!d&&c.position==="fixed"&&(o=null),(i?!d&&!o:!d&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Te(s)&&!d&&Bn(e,s))?r=r.filter(f=>f!==s):o=c,s=ue(s)}return t.set(e,r),r}function Pi(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?at(t)?[]:Mi(t,this._c):[].concat(n),r],c=s[0],d=s.reduce((l,f)=>{const u=dn(t,f,o);return l.top=V(u.top,l.top),l.right=ce(u.right,l.right),l.bottom=ce(u.bottom,l.bottom),l.left=V(u.left,l.left),l},dn(t,c,o));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function Ai(e){const{width:t,height:n}=Fn(e);return{width:t,height:n}}function Oi(e,t,n){const r=J(t),o=ee(t),i=n==="fixed",s=de(e,!0,i,t);let c={scrollLeft:0,scrollTop:0};const d=Q(0);if(r||!r&&!i)if((Ce(t)!=="body"||Te(o))&&(c=ut(t)),r){const p=de(t,!0,i,t);d.x=p.x+t.clientLeft,d.y=p.y+t.clientTop}else o&&(d.x=jt(o));const l=o&&!r&&!i?$n(o,c):Q(0),f=s.left+c.scrollLeft-d.x-l.x,u=s.top+c.scrollTop-d.y-l.y;return{x:f,y:u,width:s.width,height:s.height}}function gt(e){return X(e).position==="static"}function pn(e,t){if(!J(e)||X(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ee(e)===n&&(n=n.ownerDocument.body),n}function Wn(e,t){const n=G(e);if(at(e))return n;if(!J(e)){let o=ue(e);for(;o&&!xe(o);){if(Y(o)&&!gt(o))return o;o=ue(o)}return n}let r=pn(e,t);for(;r&&gi(r)&&gt(r);)r=pn(r,t);return r&&xe(r)&&gt(r)&&!kt(r)?n:r||wi(e)||n}const _i=async function(e){const t=this.getOffsetParent||Wn,n=this.getDimensions,r=await n(e.floating);return{reference:Oi(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ni(e){return X(e).direction==="rtl"}const Ii={convertOffsetParentRelativeRectToViewportRelativeRect:bi,getDocumentElement:ee,getClippingRect:Pi,getOffsetParent:Wn,getElementRects:_i,getClientRects:Ci,getDimensions:Ai,getScale:we,isElement:Y,isRTL:Ni};function Un(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Di(e,t){let n=null,r;const o=ee(e);function i(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function s(c,d){c===void 0&&(c=!1),d===void 0&&(d=1),i();const l=e.getBoundingClientRect(),{left:f,top:u,width:p,height:h}=l;if(c||t(),!p||!h)return;const g=We(u),m=We(o.clientWidth-(f+p)),v=We(o.clientHeight-(u+h)),y=We(f),x={rootMargin:-g+"px "+-m+"px "+-v+"px "+-y+"px",threshold:V(0,ce(1,d))||1};let b=!0;function E(M){const S=M[0].intersectionRatio;if(S!==d){if(!b)return s();S?s(!1,S):r=setTimeout(()=>{s(!1,1e-7)},1e3)}S===1&&!Un(l,e.getBoundingClientRect())&&s(),b=!1}try{n=new IntersectionObserver(E,{...x,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,x)}n.observe(e)}return s(!0),i}function Ti(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:d=!1}=r,l=Ft(e),f=o||i?[...l?_e(l):[],..._e(t)]:[];f.forEach(y=>{o&&y.addEventListener("scroll",n,{passive:!0}),i&&y.addEventListener("resize",n)});const u=l&&c?Di(l,n):null;let p=-1,h=null;s&&(h=new ResizeObserver(y=>{let[C]=y;C&&C.target===l&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var x;(x=h)==null||x.observe(t)})),n()}),l&&!d&&h.observe(l),h.observe(t));let g,m=d?de(e):null;d&&v();function v(){const y=de(e);m&&!Un(m,y)&&n(),m=y,g=requestAnimationFrame(v)}return n(),()=>{var y;f.forEach(C=>{o&&C.removeEventListener("scroll",n),i&&C.removeEventListener("resize",n)}),u==null||u(),(y=h)==null||y.disconnect(),h=null,d&&cancelAnimationFrame(g)}}const ki=pi,Li=mi,Fi=li,ji=vi,$i=fi,mn=ui,Bi=hi,Wi=(e,t,n)=>{const r=new Map,o={platform:Ii,...n},i={...o.platform,_c:r};return ai(e,t,{...o,platform:i})};var He=typeof document<"u"?a.useLayoutEffect:a.useEffect;function tt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!tt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!tt(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Kn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function hn(e,t){const n=Kn(e);return Math.round(t*n)/n}function wt(e){const t=a.useRef(e);return He(()=>{t.current=e}),t}function Ui(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:c=!0,whileElementsMounted:d,open:l}=e,[f,u]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=a.useState(r);tt(p,r)||h(r);const[g,m]=a.useState(null),[v,y]=a.useState(null),C=a.useCallback(A=>{A!==M.current&&(M.current=A,m(A))},[]),x=a.useCallback(A=>{A!==S.current&&(S.current=A,y(A))},[]),b=i||g,E=s||v,M=a.useRef(null),S=a.useRef(null),P=a.useRef(f),I=d!=null,_=wt(d),L=wt(o),F=wt(l),T=a.useCallback(()=>{if(!M.current||!S.current)return;const A={placement:t,strategy:n,middleware:p};L.current&&(A.platform=L.current),Wi(M.current,S.current,A).then(R=>{const k={...R,isPositioned:F.current!==!1};D.current&&!tt(P.current,k)&&(P.current=k,En.flushSync(()=>{u(k)}))})},[p,t,n,L,F]);He(()=>{l===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,u(A=>({...A,isPositioned:!1})))},[l]);const D=a.useRef(!1);He(()=>(D.current=!0,()=>{D.current=!1}),[]),He(()=>{if(b&&(M.current=b),E&&(S.current=E),b&&E){if(_.current)return _.current(b,E,T);T()}},[b,E,T,_,I]);const $=a.useMemo(()=>({reference:M,floating:S,setReference:C,setFloating:x}),[C,x]),N=a.useMemo(()=>({reference:b,floating:E}),[b,E]),j=a.useMemo(()=>{const A={position:n,left:0,top:0};if(!N.floating)return A;const R=hn(N.floating,f.x),k=hn(N.floating,f.y);return c?{...A,transform:"translate("+R+"px, "+k+"px)",...Kn(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:k}},[n,c,N.floating,f.x,f.y]);return a.useMemo(()=>({...f,update:T,refs:$,elements:N,floatingStyles:j}),[f,T,$,N,j])}const Ki=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?mn({element:r.current,padding:o}).fn(n):{}:r?mn({element:r,padding:o}).fn(n):{}}}},Vi=(e,t)=>({...ki(e),options:[e,t]}),Gi=(e,t)=>({...Li(e),options:[e,t]}),zi=(e,t)=>({...Bi(e),options:[e,t]}),Hi=(e,t)=>({...Fi(e),options:[e,t]}),Yi=(e,t)=>({...ji(e),options:[e,t]}),Xi=(e,t)=>({...$i(e),options:[e,t]}),qi=(e,t)=>({...Ki(e),options:[e,t]});var Zi="Arrow",Vn=a.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return w.jsx(K.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:w.jsx("polygon",{points:"0,0 30,0 15,10"})})});Vn.displayName=Zi;var Qi=Vn;function Ji(e){const[t,n]=a.useState(void 0);return se(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,c;if("borderBoxSize"in i){const d=i.borderBoxSize,l=Array.isArray(d)?d[0]:d;s=l.inlineSize,c=l.blockSize}else s=e.offsetWidth,c=e.offsetHeight;n({width:s,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var $t="Popper",[Gn,zn]=st($t),[es,Hn]=Gn($t),Yn=e=>{const{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return w.jsx(es,{scope:t,anchor:r,onAnchorChange:o,children:n})};Yn.displayName=$t;var Xn="PopperAnchor",qn=a.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Hn(Xn,n),s=a.useRef(null),c=U(t,s),d=a.useRef(null);return a.useEffect(()=>{const l=d.current;d.current=(r==null?void 0:r.current)||s.current,l!==d.current&&i.onAnchorChange(d.current)}),r?null:w.jsx(K.div,{...o,ref:c})});qn.displayName=Xn;var Bt="PopperContent",[ts,ns]=Gn(Bt),Zn=a.forwardRef((e,t)=>{var te,Re,z,Me,Yt,Xt;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:l=[],collisionPadding:f=0,sticky:u="partial",hideWhenDetached:p=!1,updatePositionStrategy:h="optimized",onPlaced:g,...m}=e,v=Hn(Bt,n),[y,C]=a.useState(null),x=U(t,Pe=>C(Pe)),[b,E]=a.useState(null),M=Ji(b),S=(M==null?void 0:M.width)??0,P=(M==null?void 0:M.height)??0,I=r+(i!=="center"?"-"+i:""),_=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},L=Array.isArray(l)?l:[l],F=L.length>0,T={padding:_,boundary:L.filter(os),altBoundary:F},{refs:D,floatingStyles:$,placement:N,isPositioned:j,middlewareData:A}=Ui({strategy:"fixed",placement:I,whileElementsMounted:(...Pe)=>Ti(...Pe,{animationFrame:h==="always"}),elements:{reference:v.anchor},middleware:[Vi({mainAxis:o+P,alignmentAxis:s}),d&&Gi({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?zi():void 0,...T}),d&&Hi({...T}),Yi({...T,apply:({elements:Pe,rects:qt,availableWidth:Zr,availableHeight:Qr})=>{const{width:Jr,height:eo}=qt.reference,Be=Pe.floating.style;Be.setProperty("--radix-popper-available-width",`${Zr}px`),Be.setProperty("--radix-popper-available-height",`${Qr}px`),Be.setProperty("--radix-popper-anchor-width",`${Jr}px`),Be.setProperty("--radix-popper-anchor-height",`${eo}px`)}}),b&&qi({element:b,padding:c}),is({arrowWidth:S,arrowHeight:P}),p&&Xi({strategy:"referenceHidden",...T})]}),[R,k]=er(N),B=ne(g);se(()=>{j&&(B==null||B())},[j,B]);const q=(te=A.arrow)==null?void 0:te.x,Ee=(Re=A.arrow)==null?void 0:Re.y,Se=((z=A.arrow)==null?void 0:z.centerOffset)!==0,[$e,le]=a.useState();return se(()=>{y&&le(window.getComputedStyle(y).zIndex)},[y]),w.jsx("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:j?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$e,"--radix-popper-transform-origin":[(Me=A.transformOrigin)==null?void 0:Me.x,(Yt=A.transformOrigin)==null?void 0:Yt.y].join(" "),...((Xt=A.hide)==null?void 0:Xt.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:w.jsx(ts,{scope:n,placedSide:R,onArrowChange:E,arrowX:q,arrowY:Ee,shouldHideArrow:Se,children:w.jsx(K.div,{"data-side":R,"data-align":k,...m,ref:x,style:{...m.style,animation:j?void 0:"none"}})})})});Zn.displayName=Bt;var Qn="PopperArrow",rs={top:"bottom",right:"left",bottom:"top",left:"right"},Jn=a.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=ns(Qn,r),s=rs[i.placedSide];return w.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:w.jsx(Qi,{...o,ref:n,style:{...o.style,display:"block"}})})});Jn.displayName=Qn;function os(e){return e!==null}var is=e=>({name:"transformOrigin",options:e,fn(t){var v,y,C;const{placement:n,rects:r,middlewareData:o}=t,s=((v=o.arrow)==null?void 0:v.centerOffset)!==0,c=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[l,f]=er(n),u={start:"0%",center:"50%",end:"100%"}[f],p=(((y=o.arrow)==null?void 0:y.x)??0)+c/2,h=(((C=o.arrow)==null?void 0:C.y)??0)+d/2;let g="",m="";return l==="bottom"?(g=s?u:`${p}px`,m=`${-d}px`):l==="top"?(g=s?u:`${p}px`,m=`${r.floating.height+d}px`):l==="right"?(g=`${-d}px`,m=s?u:`${h}px`):l==="left"&&(g=`${r.floating.width+d}px`,m=s?u:`${h}px`),{data:{x:g,y:m}}}});function er(e){const[t,n="center"]=e.split("-");return[t,n]}var ss=Yn,cs=qn,as=Zn,us=Jn,ls="Portal",tr=a.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,i]=a.useState(!1);se(()=>i(!0),[]);const s=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return s?no.createPortal(w.jsx(K.div,{...r,ref:t}),s):null});tr.displayName=ls;function fs(e,t){return a.useReducer((n,r)=>t[n][r]??n,e)}var ke=e=>{const{present:t,children:n}=e,r=ds(t),o=typeof n=="function"?n({present:r.isPresent}):a.Children.only(n),i=U(r.ref,ps(o));return typeof n=="function"||r.isPresent?a.cloneElement(o,{ref:i}):null};ke.displayName="Presence";function ds(e){const[t,n]=a.useState(),r=a.useRef(null),o=a.useRef(e),i=a.useRef("none"),s=e?"mounted":"unmounted",[c,d]=fs(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const l=Ue(r.current);i.current=c==="mounted"?l:"none"},[c]),se(()=>{const l=r.current,f=o.current;if(f!==e){const p=i.current,h=Ue(l);e?d("MOUNT"):h==="none"||(l==null?void 0:l.display)==="none"?d("UNMOUNT"):d(f&&p!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,d]),se(()=>{if(t){let l;const f=t.ownerDocument.defaultView??window,u=h=>{const m=Ue(r.current).includes(CSS.escape(h.animationName));if(h.target===t&&m&&(d("ANIMATION_END"),!o.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",l=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},p=h=>{h.target===t&&(i.current=Ue(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{f.clearTimeout(l),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else d("ANIMATION_END")},[t,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(l=>{r.current=l?getComputedStyle(l):null,n(l)},[])}}function Ue(e){return(e==null?void 0:e.animationName)||"none"}function ps(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var ms=ot[" useInsertionEffect ".trim().toString()]||se;function hs({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=vs({defaultProp:t,onChange:n}),c=e!==void 0,d=c?e:o;{const f=a.useRef(e!==void 0);a.useEffect(()=>{const u=f.current;u!==c&&console.warn(`${r} is changing from ${u?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=c},[c,r])}const l=a.useCallback(f=>{var u;if(c){const p=gs(f)?f(e):f;p!==e&&((u=s.current)==null||u.call(s,p))}else i(f)},[c,e,i,s]);return[d,l]}function vs({defaultProp:e,onChange:t}){const[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return ms(()=>{i.current=t},[t]),a.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function gs(e){return typeof e=="function"}var yt="rovingFocusGroup.onEntryFocus",ws={bubbles:!1,cancelable:!0},Le="RovingFocusGroup",[At,nr,ys]=Mn(Le),[xs,rr]=st(Le,[ys]),[bs,Cs]=xs(Le),or=a.forwardRef((e,t)=>w.jsx(At.Provider,{scope:e.__scopeRovingFocusGroup,children:w.jsx(At.Slot,{scope:e.__scopeRovingFocusGroup,children:w.jsx(Es,{...e,ref:t})})}));or.displayName=Le;var Es=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:l,preventScrollOnEntryFocus:f=!1,...u}=e,p=a.useRef(null),h=U(t,p),g=Pn(i),[m,v]=hs({prop:s,defaultProp:c??null,onChange:d,caller:Le}),[y,C]=a.useState(!1),x=ne(l),b=nr(n),E=a.useRef(!1),[M,S]=a.useState(0);return a.useEffect(()=>{const P=p.current;if(P)return P.addEventListener(yt,x),()=>P.removeEventListener(yt,x)},[x]),w.jsx(bs,{scope:n,orientation:r,dir:g,loop:o,currentTabStopId:m,onItemFocus:a.useCallback(P=>v(P),[v]),onItemShiftTab:a.useCallback(()=>C(!0),[]),onFocusableItemAdd:a.useCallback(()=>S(P=>P+1),[]),onFocusableItemRemove:a.useCallback(()=>S(P=>P-1),[]),children:w.jsx(K.div,{tabIndex:y||M===0?-1:0,"data-orientation":r,...u,ref:h,style:{outline:"none",...e.style},onMouseDown:O(e.onMouseDown,()=>{E.current=!0}),onFocus:O(e.onFocus,P=>{const I=!E.current;if(P.target===P.currentTarget&&I&&!y){const _=new CustomEvent(yt,ws);if(P.currentTarget.dispatchEvent(_),!_.defaultPrevented){const L=b().filter(N=>N.focusable),F=L.find(N=>N.active),T=L.find(N=>N.id===m),$=[F,T,...L].filter(Boolean).map(N=>N.ref.current);cr($,f)}}E.current=!1}),onBlur:O(e.onBlur,()=>C(!1))})})}),ir="RovingFocusGroupItem",sr=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:s,...c}=e,d=Jo(),l=i||d,f=Cs(ir,n),u=f.currentTabStopId===l,p=nr(n),{onFocusableItemAdd:h,onFocusableItemRemove:g,currentTabStopId:m}=f;return a.useEffect(()=>{if(r)return h(),()=>g()},[r,h,g]),w.jsx(At.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:w.jsx(K.span,{tabIndex:u?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:O(e.onMouseDown,v=>{r?f.onItemFocus(l):v.preventDefault()}),onFocus:O(e.onFocus,()=>f.onItemFocus(l)),onKeyDown:O(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){f.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const y=Ms(v,f.orientation,f.dir);if(y!==void 0){if(v.metaKey||v.ctrlKey||v.altKey||v.shiftKey)return;v.preventDefault();let x=p().filter(b=>b.focusable).map(b=>b.ref.current);if(y==="last")x.reverse();else if(y==="prev"||y==="next"){y==="prev"&&x.reverse();const b=x.indexOf(v.currentTarget);x=f.loop?Ps(x,b+1):x.slice(b+1)}setTimeout(()=>cr(x))}}),children:typeof s=="function"?s({isCurrentTabStop:u,hasTabStop:m!=null}):s})})});sr.displayName=ir;var Ss={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Rs(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Ms(e,t,n){const r=Rs(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Ss[r]}function cr(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Ps(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var As=or,Os=sr,_s=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},he=new WeakMap,Ke=new WeakMap,Ve={},xt=0,ar=function(e){return e&&(e.host||ar(e.parentNode))},Ns=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=ar(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Is=function(e,t,n,r){var o=Ns(t,Array.isArray(e)?e:[e]);Ve[n]||(Ve[n]=new WeakMap);var i=Ve[n],s=[],c=new Set,d=new Set(o),l=function(u){!u||c.has(u)||(c.add(u),l(u.parentNode))};o.forEach(l);var f=function(u){!u||d.has(u)||Array.prototype.forEach.call(u.children,function(p){if(c.has(p))f(p);else try{var h=p.getAttribute(r),g=h!==null&&h!=="false",m=(he.get(p)||0)+1,v=(i.get(p)||0)+1;he.set(p,m),i.set(p,v),s.push(p),m===1&&g&&Ke.set(p,!0),v===1&&p.setAttribute(n,"true"),g||p.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",p,y)}})};return f(t),c.clear(),xt++,function(){s.forEach(function(u){var p=he.get(u)-1,h=i.get(u)-1;he.set(u,p),i.set(u,h),p||(Ke.has(u)||u.removeAttribute(r),Ke.delete(u)),h||u.removeAttribute(n)}),xt--,xt||(he=new WeakMap,he=new WeakMap,Ke=new WeakMap,Ve={})}},Ds=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=_s(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Is(r,o,n,"aria-hidden")):function(){return null}},Z=function(){return Z=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Z.apply(this,arguments)};function ur(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Ts(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Ye="right-scroll-bar-position",Xe="width-before-scroll-bar",ks="with-scroll-bars-hidden",Ls="--removed-body-scroll-bar-size";function bt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Fs(e,t){var n=a.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var js=typeof window<"u"?a.useLayoutEffect:a.useEffect,vn=new WeakMap;function $s(e,t){var n=Fs(null,function(r){return e.forEach(function(o){return bt(o,r)})});return js(function(){var r=vn.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(c){i.has(c)||bt(c,null)}),i.forEach(function(c){o.has(c)||bt(c,s)})}vn.set(n,e)},[e]),n}function Bs(e){return e}function Ws(e,t){t===void 0&&(t=Bs);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(c){return c!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(c){return i(c)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var c=n;n=[],c.forEach(i),s=n}var d=function(){var f=s;s=[],f.forEach(i)},l=function(){return Promise.resolve().then(d)};l(),n={push:function(f){s.push(f),l()},filter:function(f){return s=s.filter(f),n}}}};return o}function Us(e){e===void 0&&(e={});var t=Ws(null);return t.options=Z({async:!0,ssr:!1},e),t}var lr=function(e){var t=e.sideCar,n=ur(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return a.createElement(r,Z({},n))};lr.isSideCarExport=!0;function Ks(e,t){return e.useMedium(t),lr}var fr=Us(),Ct=function(){},lt=a.forwardRef(function(e,t){var n=a.useRef(null),r=a.useState({onScrollCapture:Ct,onWheelCapture:Ct,onTouchMoveCapture:Ct}),o=r[0],i=r[1],s=e.forwardProps,c=e.children,d=e.className,l=e.removeScrollBar,f=e.enabled,u=e.shards,p=e.sideCar,h=e.noIsolation,g=e.inert,m=e.allowPinchZoom,v=e.as,y=v===void 0?"div":v,C=e.gapMode,x=ur(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=p,E=$s([n,t]),M=Z(Z({},x),o);return a.createElement(a.Fragment,null,f&&a.createElement(b,{sideCar:fr,removeScrollBar:l,shards:u,noIsolation:h,inert:g,setCallbacks:i,allowPinchZoom:!!m,lockRef:n,gapMode:C}),s?a.cloneElement(a.Children.only(c),Z(Z({},M),{ref:E})):a.createElement(y,Z({},M,{className:d,ref:E}),c))});lt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};lt.classNames={fullWidth:Xe,zeroRight:Ye};var Vs=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Gs(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Vs();return t&&e.setAttribute("nonce",t),e}function zs(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Hs(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Ys=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Gs())&&(zs(t,n),Hs(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Xs=function(){var e=Ys();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},dr=function(){var e=Xs(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},qs={left:0,top:0,right:0,gap:0},Et=function(e){return parseInt(e||"",10)||0},Zs=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Et(n),Et(r),Et(o)]},Qs=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return qs;var t=Zs(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Js=dr(),ye="data-scroll-locked",ec=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(ks,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(ye,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ye,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Xe,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Ye," .").concat(Ye,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Xe," .").concat(Xe,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ye,`] {
    `).concat(Ls,": ").concat(c,`px;
  }
`)},gn=function(){var e=parseInt(document.body.getAttribute(ye)||"0",10);return isFinite(e)?e:0},tc=function(){a.useEffect(function(){return document.body.setAttribute(ye,(gn()+1).toString()),function(){var e=gn()-1;e<=0?document.body.removeAttribute(ye):document.body.setAttribute(ye,e.toString())}},[])},nc=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;tc();var i=a.useMemo(function(){return Qs(o)},[o]);return a.createElement(Js,{styles:ec(i,!t,o,n?"":"!important")})},Ot=!1;if(typeof window<"u")try{var Ge=Object.defineProperty({},"passive",{get:function(){return Ot=!0,!0}});window.addEventListener("test",Ge,Ge),window.removeEventListener("test",Ge,Ge)}catch{Ot=!1}var ve=Ot?{passive:!1}:!1,rc=function(e){return e.tagName==="TEXTAREA"},pr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!rc(e)&&n[t]==="visible")},oc=function(e){return pr(e,"overflowY")},ic=function(e){return pr(e,"overflowX")},wn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=mr(e,r);if(o){var i=hr(e,r),s=i[1],c=i[2];if(s>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},sc=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},cc=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},mr=function(e,t){return e==="v"?oc(t):ic(t)},hr=function(e,t){return e==="v"?sc(t):cc(t)},ac=function(e,t){return e==="h"&&t==="rtl"?-1:1},uc=function(e,t,n,r,o){var i=ac(e,window.getComputedStyle(t).direction),s=i*r,c=n.target,d=t.contains(c),l=!1,f=s>0,u=0,p=0;do{var h=hr(e,c),g=h[0],m=h[1],v=h[2],y=m-v-i*g;(g||y)&&mr(e,c)&&(u+=y,p+=g),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!d&&c!==document.body||d&&(t.contains(c)||t===c));return(f&&Math.abs(u)<1||!f&&Math.abs(p)<1)&&(l=!0),l},ze=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},yn=function(e){return[e.deltaX,e.deltaY]},xn=function(e){return e&&"current"in e?e.current:e},lc=function(e,t){return e[0]===t[0]&&e[1]===t[1]},fc=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},dc=0,ge=[];function pc(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(dc++)[0],i=a.useState(dr)[0],s=a.useRef(e);a.useEffect(function(){s.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=Ts([e.lockRef.current],(e.shards||[]).map(xn),!0).filter(Boolean);return m.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(m,v){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!s.current.allowPinchZoom;var y=ze(m),C=n.current,x="deltaX"in m?m.deltaX:C[0]-y[0],b="deltaY"in m?m.deltaY:C[1]-y[1],E,M=m.target,S=Math.abs(x)>Math.abs(b)?"h":"v";if("touches"in m&&S==="h"&&M.type==="range")return!1;var P=wn(S,M);if(!P)return!0;if(P?E=S:(E=S==="v"?"h":"v",P=wn(S,M)),!P)return!1;if(!r.current&&"changedTouches"in m&&(x||b)&&(r.current=E),!E)return!0;var I=r.current||E;return uc(I,v,m,I==="h"?x:b)},[]),d=a.useCallback(function(m){var v=m;if(!(!ge.length||ge[ge.length-1]!==i)){var y="deltaY"in v?yn(v):ze(v),C=t.current.filter(function(E){return E.name===v.type&&(E.target===v.target||v.target===E.shadowParent)&&lc(E.delta,y)})[0];if(C&&C.should){v.cancelable&&v.preventDefault();return}if(!C){var x=(s.current.shards||[]).map(xn).filter(Boolean).filter(function(E){return E.contains(v.target)}),b=x.length>0?c(v,x[0]):!s.current.noIsolation;b&&v.cancelable&&v.preventDefault()}}},[]),l=a.useCallback(function(m,v,y,C){var x={name:m,delta:v,target:y,should:C,shadowParent:mc(y)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(b){return b!==x})},1)},[]),f=a.useCallback(function(m){n.current=ze(m),r.current=void 0},[]),u=a.useCallback(function(m){l(m.type,yn(m),m.target,c(m,e.lockRef.current))},[]),p=a.useCallback(function(m){l(m.type,ze(m),m.target,c(m,e.lockRef.current))},[]);a.useEffect(function(){return ge.push(i),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:p}),document.addEventListener("wheel",d,ve),document.addEventListener("touchmove",d,ve),document.addEventListener("touchstart",f,ve),function(){ge=ge.filter(function(m){return m!==i}),document.removeEventListener("wheel",d,ve),document.removeEventListener("touchmove",d,ve),document.removeEventListener("touchstart",f,ve)}},[]);var h=e.removeScrollBar,g=e.inert;return a.createElement(a.Fragment,null,g?a.createElement(i,{styles:fc(o)}):null,h?a.createElement(nc,{gapMode:e.gapMode}):null)}function mc(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const hc=Ks(fr,pc);var vr=a.forwardRef(function(e,t){return a.createElement(lt,Z({},e,{ref:t,sideCar:hc}))});vr.classNames=lt.classNames;var _t=["Enter"," "],vc=["ArrowDown","PageUp","Home"],gr=["ArrowUp","PageDown","End"],gc=[...vc,...gr],wc={ltr:[..._t,"ArrowRight"],rtl:[..._t,"ArrowLeft"]},yc={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Fe="Menu",[Ne,xc,bc]=Mn(Fe),[pe,wr]=st(Fe,[bc,zn,rr]),ft=zn(),yr=rr(),[Cc,me]=pe(Fe),[Ec,je]=pe(Fe),xr=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:s=!0}=e,c=ft(t),[d,l]=a.useState(null),f=a.useRef(!1),u=ne(i),p=Pn(o);return a.useEffect(()=>{const h=()=>{f.current=!0,document.addEventListener("pointerdown",g,{capture:!0,once:!0}),document.addEventListener("pointermove",g,{capture:!0,once:!0})},g=()=>f.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",g,{capture:!0}),document.removeEventListener("pointermove",g,{capture:!0})}},[]),w.jsx(ss,{...c,children:w.jsx(Cc,{scope:t,open:n,onOpenChange:u,content:d,onContentChange:l,children:w.jsx(Ec,{scope:t,onClose:a.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:f,dir:p,modal:s,children:r})})})};xr.displayName=Fe;var Sc="MenuAnchor",Wt=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=ft(n);return w.jsx(cs,{...o,...r,ref:t})});Wt.displayName=Sc;var Ut="MenuPortal",[Rc,br]=pe(Ut,{forceMount:void 0}),Cr=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=me(Ut,t);return w.jsx(Rc,{scope:t,forceMount:n,children:w.jsx(ke,{present:n||i.open,children:w.jsx(tr,{asChild:!0,container:o,children:r})})})};Cr.displayName=Ut;var H="MenuContent",[Mc,Kt]=pe(H),Er=a.forwardRef((e,t)=>{const n=br(H,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=me(H,e.__scopeMenu),s=je(H,e.__scopeMenu);return w.jsx(Ne.Provider,{scope:e.__scopeMenu,children:w.jsx(ke,{present:r||i.open,children:w.jsx(Ne.Slot,{scope:e.__scopeMenu,children:s.modal?w.jsx(Pc,{...o,ref:t}):w.jsx(Ac,{...o,ref:t})})})})}),Pc=a.forwardRef((e,t)=>{const n=me(H,e.__scopeMenu),r=a.useRef(null),o=U(t,r);return a.useEffect(()=>{const i=r.current;if(i)return Ds(i)},[]),w.jsx(Vt,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:O(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Ac=a.forwardRef((e,t)=>{const n=me(H,e.__scopeMenu);return w.jsx(Vt,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Oc=Ze("MenuContent.ScrollLock"),Vt=a.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:l,onPointerDownOutside:f,onFocusOutside:u,onInteractOutside:p,onDismiss:h,disableOutsideScroll:g,...m}=e,v=me(H,n),y=je(H,n),C=ft(n),x=yr(n),b=xc(n),[E,M]=a.useState(null),S=a.useRef(null),P=U(t,S,v.onContentChange),I=a.useRef(0),_=a.useRef(""),L=a.useRef(0),F=a.useRef(null),T=a.useRef("right"),D=a.useRef(0),$=g?vr:a.Fragment,N=g?{as:Oc,allowPinchZoom:!0}:void 0,j=R=>{var te,Re;const k=_.current+R,B=b().filter(z=>!z.disabled),q=document.activeElement,Ee=(te=B.find(z=>z.ref.current===q))==null?void 0:te.textValue,Se=B.map(z=>z.textValue),$e=Wc(Se,k,Ee),le=(Re=B.find(z=>z.textValue===$e))==null?void 0:Re.ref.current;(function z(Me){_.current=Me,window.clearTimeout(I.current),Me!==""&&(I.current=window.setTimeout(()=>z(""),1e3))})(k),le&&setTimeout(()=>le.focus())};a.useEffect(()=>()=>window.clearTimeout(I.current),[]),Ko();const A=a.useCallback(R=>{var B,q;return T.current===((B=F.current)==null?void 0:B.side)&&Kc(R,(q=F.current)==null?void 0:q.area)},[]);return w.jsx(Mc,{scope:n,searchRef:_,onItemEnter:a.useCallback(R=>{A(R)&&R.preventDefault()},[A]),onItemLeave:a.useCallback(R=>{var k;A(R)||((k=S.current)==null||k.focus(),M(null))},[A]),onTriggerLeave:a.useCallback(R=>{A(R)&&R.preventDefault()},[A]),pointerGraceTimerRef:L,onPointerGraceIntentChange:a.useCallback(R=>{F.current=R},[]),children:w.jsx($,{...N,children:w.jsx(In,{asChild:!0,trapped:o,onMountAutoFocus:O(i,R=>{var k;R.preventDefault(),(k=S.current)==null||k.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:w.jsx(_n,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:l,onPointerDownOutside:f,onFocusOutside:u,onInteractOutside:p,onDismiss:h,children:w.jsx(As,{asChild:!0,...x,dir:y.dir,orientation:"vertical",loop:r,currentTabStopId:E,onCurrentTabStopIdChange:M,onEntryFocus:O(d,R=>{y.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:w.jsx(as,{role:"menu","aria-orientation":"vertical","data-state":$r(v.open),"data-radix-menu-content":"",dir:y.dir,...C,...m,ref:P,style:{outline:"none",...m.style},onKeyDown:O(m.onKeyDown,R=>{const B=R.target.closest("[data-radix-menu-content]")===R.currentTarget,q=R.ctrlKey||R.altKey||R.metaKey,Ee=R.key.length===1;B&&(R.key==="Tab"&&R.preventDefault(),!q&&Ee&&j(R.key));const Se=S.current;if(R.target!==Se||!gc.includes(R.key))return;R.preventDefault();const le=b().filter(te=>!te.disabled).map(te=>te.ref.current);gr.includes(R.key)&&le.reverse(),$c(le)}),onBlur:O(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(I.current),_.current="")}),onPointerMove:O(e.onPointerMove,Ie(R=>{const k=R.target,B=D.current!==R.clientX;if(R.currentTarget.contains(k)&&B){const q=R.clientX>D.current?"right":"left";T.current=q,D.current=R.clientX}}))})})})})})})});Er.displayName=H;var _c="MenuGroup",Gt=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return w.jsx(K.div,{role:"group",...r,ref:t})});Gt.displayName=_c;var Nc="MenuLabel",Sr=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return w.jsx(K.div,{...r,ref:t})});Sr.displayName=Nc;var nt="MenuItem",bn="menu.itemSelect",dt=a.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,i=a.useRef(null),s=je(nt,e.__scopeMenu),c=Kt(nt,e.__scopeMenu),d=U(t,i),l=a.useRef(!1),f=()=>{const u=i.current;if(!n&&u){const p=new CustomEvent(bn,{bubbles:!0,cancelable:!0});u.addEventListener(bn,h=>r==null?void 0:r(h),{once:!0}),An(u,p),p.defaultPrevented?l.current=!1:s.onClose()}};return w.jsx(Rr,{...o,ref:d,disabled:n,onClick:O(e.onClick,f),onPointerDown:u=>{var p;(p=e.onPointerDown)==null||p.call(e,u),l.current=!0},onPointerUp:O(e.onPointerUp,u=>{var p;l.current||(p=u.currentTarget)==null||p.click()}),onKeyDown:O(e.onKeyDown,u=>{const p=c.searchRef.current!=="";n||p&&u.key===" "||_t.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});dt.displayName=nt;var Rr=a.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,s=Kt(nt,n),c=yr(n),d=a.useRef(null),l=U(t,d),[f,u]=a.useState(!1),[p,h]=a.useState("");return a.useEffect(()=>{const g=d.current;g&&h((g.textContent??"").trim())},[i.children]),w.jsx(Ne.ItemSlot,{scope:n,disabled:r,textValue:o??p,children:w.jsx(Os,{asChild:!0,...c,focusable:!r,children:w.jsx(K.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:l,onPointerMove:O(e.onPointerMove,Ie(g=>{r?s.onItemLeave(g):(s.onItemEnter(g),g.defaultPrevented||g.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:O(e.onPointerLeave,Ie(g=>s.onItemLeave(g))),onFocus:O(e.onFocus,()=>u(!0)),onBlur:O(e.onBlur,()=>u(!1))})})})}),Ic="MenuCheckboxItem",Mr=a.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return w.jsx(Nr,{scope:e.__scopeMenu,checked:n,children:w.jsx(dt,{role:"menuitemcheckbox","aria-checked":rt(n)?"mixed":n,...o,ref:t,"data-state":Ht(n),onSelect:O(o.onSelect,()=>r==null?void 0:r(rt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Mr.displayName=Ic;var Pr="MenuRadioGroup",[Dc,Tc]=pe(Pr,{value:void 0,onValueChange:()=>{}}),Ar=a.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,i=ne(r);return w.jsx(Dc,{scope:e.__scopeMenu,value:n,onValueChange:i,children:w.jsx(Gt,{...o,ref:t})})});Ar.displayName=Pr;var Or="MenuRadioItem",_r=a.forwardRef((e,t)=>{const{value:n,...r}=e,o=Tc(Or,e.__scopeMenu),i=n===o.value;return w.jsx(Nr,{scope:e.__scopeMenu,checked:i,children:w.jsx(dt,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":Ht(i),onSelect:O(r.onSelect,()=>{var s;return(s=o.onValueChange)==null?void 0:s.call(o,n)},{checkForDefaultPrevented:!1})})})});_r.displayName=Or;var zt="MenuItemIndicator",[Nr,kc]=pe(zt,{checked:!1}),Ir=a.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,i=kc(zt,n);return w.jsx(ke,{present:r||rt(i.checked)||i.checked===!0,children:w.jsx(K.span,{...o,ref:t,"data-state":Ht(i.checked)})})});Ir.displayName=zt;var Lc="MenuSeparator",Dr=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return w.jsx(K.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});Dr.displayName=Lc;var Fc="MenuArrow",Tr=a.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=ft(n);return w.jsx(us,{...o,...r,ref:t})});Tr.displayName=Fc;var jc="MenuSub",[Ba,kr]=pe(jc),Ae="MenuSubTrigger",Lr=a.forwardRef((e,t)=>{const n=me(Ae,e.__scopeMenu),r=je(Ae,e.__scopeMenu),o=kr(Ae,e.__scopeMenu),i=Kt(Ae,e.__scopeMenu),s=a.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=i,l={__scopeMenu:e.__scopeMenu},f=a.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return a.useEffect(()=>f,[f]),a.useEffect(()=>{const u=c.current;return()=>{window.clearTimeout(u),d(null)}},[c,d]),w.jsx(Wt,{asChild:!0,...l,children:w.jsx(Rr,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":$r(n.open),...e,ref:Nt(t,o.onTriggerChange),onClick:u=>{var p;(p=e.onClick)==null||p.call(e,u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:O(e.onPointerMove,Ie(u=>{i.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!s.current&&(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:O(e.onPointerLeave,Ie(u=>{var h,g;f();const p=(h=n.content)==null?void 0:h.getBoundingClientRect();if(p){const m=(g=n.content)==null?void 0:g.dataset.side,v=m==="right",y=v?-5:5,C=p[v?"left":"right"],x=p[v?"right":"left"];i.onPointerGraceIntentChange({area:[{x:u.clientX+y,y:u.clientY},{x:C,y:p.top},{x,y:p.top},{x,y:p.bottom},{x:C,y:p.bottom}],side:m}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(u),u.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:O(e.onKeyDown,u=>{var h;const p=i.searchRef.current!=="";e.disabled||p&&u.key===" "||wc[r.dir].includes(u.key)&&(n.onOpenChange(!0),(h=n.content)==null||h.focus(),u.preventDefault())})})})});Lr.displayName=Ae;var Fr="MenuSubContent",jr=a.forwardRef((e,t)=>{const n=br(H,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=me(H,e.__scopeMenu),s=je(H,e.__scopeMenu),c=kr(Fr,e.__scopeMenu),d=a.useRef(null),l=U(t,d);return w.jsx(Ne.Provider,{scope:e.__scopeMenu,children:w.jsx(ke,{present:r||i.open,children:w.jsx(Ne.Slot,{scope:e.__scopeMenu,children:w.jsx(Vt,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:l,align:"start",side:s.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{var u;s.isUsingKeyboardRef.current&&((u=d.current)==null||u.focus()),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:O(e.onFocusOutside,f=>{f.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:O(e.onEscapeKeyDown,f=>{s.onClose(),f.preventDefault()}),onKeyDown:O(e.onKeyDown,f=>{var h;const u=f.currentTarget.contains(f.target),p=yc[s.dir].includes(f.key);u&&p&&(i.onOpenChange(!1),(h=c.trigger)==null||h.focus(),f.preventDefault())})})})})})});jr.displayName=Fr;function $r(e){return e?"open":"closed"}function rt(e){return e==="indeterminate"}function Ht(e){return rt(e)?"indeterminate":e?"checked":"unchecked"}function $c(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Bc(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Wc(e,t,n){const o=t.length>1&&Array.from(t).every(l=>l===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let s=Bc(e,Math.max(i,0));o.length===1&&(s=s.filter(l=>l!==n));const d=s.find(l=>l.toLowerCase().startsWith(o.toLowerCase()));return d!==n?d:void 0}function Uc(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const c=t[i],d=t[s],l=c.x,f=c.y,u=d.x,p=d.y;f>r!=p>r&&n<(u-l)*(r-f)/(p-f)+l&&(o=!o)}return o}function Kc(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Uc(n,t)}function Ie(e){return t=>t.pointerType==="mouse"?e(t):void 0}var Vc=xr,Gc=Wt,zc=Cr,Hc=Er,Yc=Gt,Xc=Sr,qc=dt,Zc=Mr,Qc=Ar,Jc=_r,ea=Ir,ta=Dr,na=Tr,ra=Lr,oa=jr,ia=ot[" useId ".trim().toString()]||(()=>{}),sa=0;function Cn(e){const[t,n]=a.useState(ia());return Rn(()=>{n(r=>r??String(sa++))},[e]),e||(t?`radix-${t}`:"")}var pt="DropdownMenu",[ca,Wa]=po(pt,[wr]),W=wr(),[aa,Br]=ca(pt),Wr=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:s,modal:c=!0}=e,d=W(t),l=a.useRef(null),[f,u]=vo({prop:o,defaultProp:i??!1,onChange:s,caller:pt});return w.jsx(aa,{scope:t,triggerId:Cn(),triggerRef:l,contentId:Cn(),open:f,onOpenChange:u,onOpenToggle:a.useCallback(()=>u(p=>!p),[u]),modal:c,children:w.jsx(Vc,{...d,open:f,onOpenChange:u,dir:r,modal:c,children:n})})};Wr.displayName=pt;var Ur="DropdownMenuTrigger",Kr=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=Br(Ur,n),s=W(n);return w.jsx(Gc,{asChild:!0,...s,children:w.jsx(Mo.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Sn(t,i.triggerRef),onPointerDown:qe(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(i.onOpenToggle(),i.open||c.preventDefault())}),onKeyDown:qe(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&i.onOpenToggle(),c.key==="ArrowDown"&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});Kr.displayName=Ur;var ua="DropdownMenuPortal",Vr=e=>{const{__scopeDropdownMenu:t,...n}=e,r=W(t);return w.jsx(zc,{...r,...n})};Vr.displayName=ua;var Gr="DropdownMenuContent",zr=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Br(Gr,n),i=W(n),s=a.useRef(!1);return w.jsx(Hc,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:qe(e.onCloseAutoFocus,c=>{var d;s.current||(d=o.triggerRef.current)==null||d.focus(),s.current=!1,c.preventDefault()}),onInteractOutside:qe(e.onInteractOutside,c=>{const d=c.detail.originalEvent,l=d.button===0&&d.ctrlKey===!0,f=d.button===2||l;(!o.modal||f)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});zr.displayName=Gr;var la="DropdownMenuGroup",Hr=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(Yc,{...o,...r,ref:t})});Hr.displayName=la;var fa="DropdownMenuLabel",Yr=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(Xc,{...o,...r,ref:t})});Yr.displayName=fa;var da="DropdownMenuItem",Xr=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(qc,{...o,...r,ref:t})});Xr.displayName=da;var pa="DropdownMenuCheckboxItem",ma=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(Zc,{...o,...r,ref:t})});ma.displayName=pa;var ha="DropdownMenuRadioGroup",va=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(Qc,{...o,...r,ref:t})});va.displayName=ha;var ga="DropdownMenuRadioItem",wa=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(Jc,{...o,...r,ref:t})});wa.displayName=ga;var ya="DropdownMenuItemIndicator",xa=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(ea,{...o,...r,ref:t})});xa.displayName=ya;var ba="DropdownMenuSeparator",qr=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(ta,{...o,...r,ref:t})});qr.displayName=ba;var Ca="DropdownMenuArrow",Ea=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(na,{...o,...r,ref:t})});Ea.displayName=Ca;var Sa="DropdownMenuSubTrigger",Ra=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(ra,{...o,...r,ref:t})});Ra.displayName=Sa;var Ma="DropdownMenuSubContent",Pa=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=W(n);return w.jsx(oa,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Pa.displayName=Ma;var Aa=Wr,Oa=Kr,_a=Vr,Na=zr,Ia=Hr,Da=Yr,Ta=Xr,ka=qr;function Ua({...e}){return w.jsx(Aa,{"data-slot":"dropdown-menu",...e})}function Ka({...e}){return w.jsx(Oa,{"data-slot":"dropdown-menu-trigger",...e})}function Va({className:e,sideOffset:t=4,...n}){return w.jsx(_a,{children:w.jsx(Na,{"data-slot":"dropdown-menu-content",sideOffset:t,className:it("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function Ga({...e}){return w.jsx(Ia,{"data-slot":"dropdown-menu-group",...e})}function za({className:e,inset:t,variant:n="default",...r}){return w.jsx(Ta,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:it("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function Ha({className:e,inset:t,...n}){return w.jsx(Da,{"data-slot":"dropdown-menu-label","data-inset":t,className:it("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function Ya({className:e,...t}){return w.jsx(ka,{"data-slot":"dropdown-menu-separator",className:it("bg-border -mx-1 my-1 h-px",e),...t})}function Xa(){const{props:e}=to(),[t,n]=a.useState([]);a.useEffect(()=>{var c,d,l,f;const s=[];(c=e.flash)!=null&&c.success&&s.push({id:Date.now()+Math.random(),type:"success",message:e.flash.success}),(d=e.flash)!=null&&d.error&&s.push({id:Date.now()+Math.random(),type:"error",message:e.flash.error}),(l=e.flash)!=null&&l.warning&&s.push({id:Date.now()+Math.random(),type:"warning",message:e.flash.warning}),(f=e.flash)!=null&&f.info&&s.push({id:Date.now()+Math.random(),type:"info",message:e.flash.info}),s.length>0&&(n(u=>[...u,...s]),s.forEach(u=>{setTimeout(()=>{r(u.id)},5e3)}))},[e.flash]);const r=s=>{n(c=>c.filter(d=>d.id!==s))},o=s=>{switch(s){case"success":return w.jsx(so,{className:"h-5 w-5"});case"error":return w.jsx(ao,{className:"h-5 w-5"});case"warning":return w.jsx(oo,{className:"h-5 w-5"});case"info":return w.jsx(Zt,{className:"h-5 w-5"});default:return w.jsx(Zt,{className:"h-5 w-5"})}},i=s=>{switch(s){case"success":return"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200";case"error":return"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200";case"info":return"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200";default:return"bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-200"}};return t.length===0?null:w.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.map(s=>w.jsxs("div",{className:`flex items-center p-4 border rounded-lg shadow-lg max-w-md animate-in slide-in-from-right-full duration-300 ${i(s.type)}`,children:[w.jsx("div",{className:"flex-shrink-0",children:o(s.type)}),w.jsx("div",{className:"ml-3 flex-1",children:w.jsx("p",{className:"text-sm font-medium",children:s.message})}),w.jsx("div",{className:"ml-4 flex-shrink-0",children:w.jsx("button",{onClick:()=>r(s.id),className:"inline-flex rounded-md p-1.5 hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current",children:w.jsx(fo,{className:"h-4 w-4"})})})]},s.id))})}export{Ua as D,Xa as F,Zt as I,vr as R,fo as X,Ka as a,Va as b,za as c,Yi as d,qi as e,Hi as f,Ti as g,Xi as h,Ds as i,Ha as j,Ya as k,zi as l,Ga as m,Vi as o,Gi as s,Ui as u};
