import{r as x,j as e,L as F,$ as y,S as i}from"./app-BWHLaRS2.js";import{A as D}from"./admin-layout-DKXm94gZ.js";import{B as d}from"./button-CUovRkQ3.js";import{C as N,a as S,b,c as w}from"./card-3ac6awgC.js";import{B as o}from"./badge-BSmw6aR2.js";import{I as L}from"./input-B4rX1XfI.js";import{S as z}from"./search-H5umLGXz.js";import{S as E}from"./shield-BEeWYpgF.js";import{c as h}from"./book-open-DxiZ0b6m.js";import{E as T}from"./eye-CbhFpv9_.js";import{T as I}from"./trash-2-DpyI9JiF.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./sun-Bmefm2yw.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],V=h("ShieldCheck",M);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]],H=h("UserCheck",B);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],q=h("UserX",R);function ce({users:t={data:[],total:0,links:[]},filters:f={},auth:k}){var g;const m=Array.isArray(f)?{}:f,[l,p]=x.useState(String(m.search||"")),[n,u]=x.useState(String(m.role||"")),[c,v]=x.useState(String(m.verified||"")),C=r=>{r.preventDefault();const a={};l.trim()&&(a.search=l.trim()),n&&(a.role=n),c&&(a.verified=c),i.get("/admin/users",a,{preserveState:!0,preserveScroll:!0})},j=(r,a)=>{const s={};l.trim()&&(s.search=l.trim()),r==="role"?(u(a),a&&(s.role=a),c&&(s.verified=c)):r==="verified"&&(v(a),a&&(s.verified=a),n&&(s.role=n)),i.get("/admin/users",s,{preserveState:!0,preserveScroll:!0})},_=()=>{p(""),u(""),v(""),i.get("/admin/users",{},{preserveState:!0,preserveScroll:!0})},U=(r,a)=>{a!==r.role&&confirm(`Are you sure you want to change ${r.name}'s role to ${a}?`)&&i.patch(`/admin/users/${r.id}/role`,{role:a},{preserveState:!0,preserveScroll:!0,onError:s=>{console.error("Role update failed:",s)}})},A=r=>{const a=r.is_active!==!1?"deactivate":"activate";confirm(`Are you sure you want to ${a} ${r.name}?`)&&i.patch(`/admin/users/${r.id}/toggle-status`,{},{preserveState:!0,preserveScroll:!0,onError:s=>{console.error("Status toggle failed:",s)}})},$=r=>{confirm(`Are you sure you want to delete ${r.name}? This action cannot be undone.`)&&i.delete(`/admin/users/${r.id}`,{preserveState:!0,onError:a=>{console.error("User deletion failed:",a)}})};return e.jsxs(D,{children:[e.jsx(F,{title:"Manage Users - Admin Dashboard"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Manage Users"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"View and manage all users in the system"})]}),e.jsxs(N,{children:[e.jsx(S,{children:e.jsx(b,{children:"Filters"})}),e.jsx(w,{children:e.jsx("form",{onSubmit:C,className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{children:e.jsx(L,{type:"text",placeholder:"Search users...",value:l,onChange:r=>p(r.target.value)})}),e.jsx("div",{children:e.jsxs("select",{value:n,onChange:r=>j("role",r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"user",children:"User"})]})}),e.jsx("div",{children:e.jsxs("select",{value:c,onChange:r=>j("verified",r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"verified",children:"Verified"}),e.jsx("option",{value:"unverified",children:"Unverified"})]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(d,{type:"submit",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),"Search"]}),e.jsx(d,{type:"button",variant:"outline",onClick:_,children:"Clear Filters"})]})]})})})]}),e.jsxs(N,{children:[e.jsx(S,{children:e.jsxs(b,{children:["Users (",(t==null?void 0:t.total)||0,")"]})}),e.jsxs(w,{children:[e.jsxs("div",{className:"space-y-4",children:[((g=t==null?void 0:t.data)==null?void 0:g.length)>0?t.data.map(r=>{var a;return e.jsx("div",{className:"border rounded-lg p-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:e.jsx("span",{className:"text-lg font-medium text-gray-700 dark:text-gray-200",children:((a=r.name)==null?void 0:a.charAt(0))||"U"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.name}),r.role==="admin"&&e.jsxs(o,{className:"bg-purple-100 text-purple-800",children:[e.jsx(E,{className:"h-3 w-3 mr-1"}),"Admin"]}),r.email_verified_at?e.jsxs(o,{className:"bg-green-100 text-green-800",children:[e.jsx(V,{className:"h-3 w-3 mr-1"}),"Verified"]}):e.jsx(o,{variant:"outline",className:"text-yellow-600",children:"Unverified"}),r.is_active===!1&&e.jsx(o,{variant:"destructive",children:"Inactive"})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-1",children:r.email}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("p",{children:["Joined: ",r.created_at?new Date(r.created_at).toLocaleDateString():"Unknown"]}),e.jsxs("p",{children:["Last active: ",r.updated_at?new Date(r.updated_at).toLocaleDateString():"Unknown"]})]})]})]}),e.jsxs("div",{className:"flex flex-col space-y-2 ml-4",children:[e.jsx(y,{href:`/admin/users/${r.id}`,children:e.jsxs(d,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"View"]})}),e.jsxs("select",{value:r.role,onChange:s=>U(r,s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"user",children:"User"}),e.jsx("option",{value:"admin",children:"Admin"})]}),e.jsx(d,{variant:r.is_active!==!1?"outline":"default",size:"sm",onClick:()=>A(r),className:"w-full",children:r.is_active!==!1?e.jsxs(e.Fragment,{children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Deactivate"]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"h-4 w-4 mr-2"}),"Activate"]})}),r.id!==k.user.id&&e.jsxs(d,{variant:"destructive",size:"sm",onClick:()=>$(r),className:"w-full",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})},r.id)}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500 text-lg",children:"No users found"})}),!1]}),t.links&&e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("div",{className:"flex space-x-2",children:t.links.map((r,a)=>e.jsx(y,{href:r.url||"#",className:`px-3 py-2 text-sm rounded-md ${r.active?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"} ${r.url?"":"opacity-50 cursor-not-allowed"}`,dangerouslySetInnerHTML:{__html:r.label}},a))})})]})]})]})]})}export{ce as default};
