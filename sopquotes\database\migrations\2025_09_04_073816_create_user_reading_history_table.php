<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_reading_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('readable_type'); // Quote, Book, Media
            $table->unsignedBigInteger('readable_id');
            $table->timestamp('viewed_at');
            $table->json('metadata')->nullable(); // Progress for books, play time for audio
            $table->timestamps();
            
            // Polymorphic index
            $table->index(['readable_type', 'readable_id']);
            $table->index(['user_id', 'viewed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_reading_history');
    }
};
