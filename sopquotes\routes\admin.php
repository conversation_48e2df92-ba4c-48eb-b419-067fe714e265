<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\QuoteController as AdminQuoteController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use App\Http\Controllers\Admin\MediaController as AdminMediaController;
use App\Http\Controllers\Admin\SettingsController as AdminSettingsController;
use Illuminate\Support\Facades\Route;

// Admin routes - require authentication and admin role
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Admin dashboard
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');

    // Quote management
    Route::resource('quotes', AdminQuoteController::class);
    Route::patch('quotes/{quote}/toggle-featured', [AdminQuoteController::class, 'toggleFeatured'])
        ->name('quotes.toggle-featured');

    // User management
    Route::resource('users', AdminUserController::class)->except(['edit', 'show']);
    Route::patch('users/{user}/role', [AdminUserController::class, 'updateRole'])
        ->name('users.update-role');
    Route::patch('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])
        ->name('users.toggle-status');

    // Category management
    Route::resource('categories', AdminCategoryController::class);
    Route::patch('categories/{category}/toggle-status', [AdminCategoryController::class, 'toggleStatus'])
        ->name('categories.toggle-status');

    // Media management
    Route::resource('media', AdminMediaController::class);
    Route::patch('media/{media}/toggle-featured', [AdminMediaController::class, 'toggleFeatured'])
        ->name('media.toggle-featured');
    Route::patch('media/{media}/toggle-status', [AdminMediaController::class, 'toggleStatus'])
        ->name('media.toggle-status');



    // Settings management
    Route::get('settings', [AdminSettingsController::class, 'index'])->name('settings.index');
    Route::patch('settings', [AdminSettingsController::class, 'update'])->name('settings.update');
    Route::patch('settings/reset', [AdminSettingsController::class, 'reset'])->name('settings.reset');
});
