<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Quote;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Random quote endpoint
Route::get('/quotes/random', function () {
    $quote = Quote::with(['category', 'creator'])
        ->inRandomOrder()
        ->first();
    
    if (!$quote) {
        return response()->json(['error' => 'No quotes found'], 404);
    }
    
    return response()->json([
        'quote' => $quote
    ]);
});
