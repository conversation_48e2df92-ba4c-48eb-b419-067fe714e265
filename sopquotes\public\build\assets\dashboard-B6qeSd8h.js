import{j as e,L as f,$ as l}from"./app-BWHLaRS2.js";import{A as p}from"./admin-layout-DKXm94gZ.js";import{B as r}from"./button-CUovRkQ3.js";import{C as a,a as n,b as d,c as t}from"./card-3ac6awgC.js";import{B as m}from"./badge-BSmw6aR2.js";import{c as u,B as h}from"./book-open-DxiZ0b6m.js";import{U as j}from"./users-BHOsrKYW.js";import{D as N}from"./download-Bv0PHygq.js";import{P as g}from"./plus-CzNwqLHA.js";import{E as y}from"./eye-CbhFpv9_.js";import{S as w}from"./square-pen-BLfc3BBH.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./sun-Bmefm2yw.js";import"./volume-2-DIbtoXia.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],b=u("TrendingUp",v);function R({stats:s,recentQuotes:c,recentUsers:x,auth:A}){return e.jsxs(p,{children:[e.jsx(f,{title:"Admin Dashboard - SOP Quotes"}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Manage your SOP Quotes content and users"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsxs(a,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Total Quotes"}),e.jsx(h,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(t,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(s==null?void 0:s.totalQuotes)||0}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["+",(s==null?void 0:s.newQuotesThisMonth)||0," this month"]})]})]}),e.jsxs(a,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Total Users"}),e.jsx(j,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(t,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(s==null?void 0:s.totalUsers)||0}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["+",(s==null?void 0:s.newUsersThisMonth)||0," this month"]})]})]}),e.jsxs(a,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Featured Quotes"}),e.jsx(b,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(t,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(s==null?void 0:s.featuredQuotes)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Active featured content"})]})]}),e.jsxs(a,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(d,{className:"text-sm font-medium",children:"Total Downloads"}),e.jsx(N,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(t,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(s==null?void 0:s.totalDownloads)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"PDF & Audio downloads"})]})]})]}),e.jsxs(a,{children:[e.jsx(n,{children:e.jsx(d,{children:"Quick Actions"})}),e.jsx(t,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx(l,{href:"/admin/quotes/create",children:e.jsxs(r,{className:"w-full h-20 flex flex-col items-center justify-center space-y-2",children:[e.jsx(g,{className:"h-6 w-6"}),e.jsx("span",{children:"Create New Quote"})]})}),e.jsx(l,{href:"/admin/quotes",children:e.jsxs(r,{variant:"outline",className:"w-full h-20 flex flex-col items-center justify-center space-y-2",children:[e.jsx(h,{className:"h-6 w-6"}),e.jsx("span",{children:"Manage Quotes"})]})}),e.jsx(l,{href:"/admin/users",children:e.jsxs(r,{variant:"outline",className:"w-full h-20 flex flex-col items-center justify-center space-y-2",children:[e.jsx(j,{className:"h-6 w-6"}),e.jsx("span",{children:"Manage Users"})]})})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs(a,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between",children:[e.jsx(d,{children:"Recent Quotes"}),e.jsx(l,{href:"/admin/quotes",children:e.jsx(r,{variant:"outline",size:"sm",children:"View All"})})]}),e.jsx(t,{children:e.jsxs("div",{className:"space-y-4",children:[c==null?void 0:c.map(i=>e.jsxs("div",{className:"flex items-start justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-sm font-medium line-clamp-2",children:['"',i.text,'"']}),e.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[i.is_featured&&e.jsx(m,{variant:"secondary",children:"Featured"}),i.category&&e.jsx(m,{variant:"outline",children:i.category.name})]}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Created ",new Date(i.created_at).toLocaleDateString()]})]}),e.jsxs("div",{className:"flex space-x-1 ml-4",children:[e.jsx(l,{href:`/quotes/${i.id}`,children:e.jsx(r,{variant:"ghost",size:"sm",children:e.jsx(y,{className:"h-4 w-4"})})}),e.jsx(l,{href:`/admin/quotes/${i.id}/edit`,children:e.jsx(r,{variant:"ghost",size:"sm",children:e.jsx(w,{className:"h-4 w-4"})})})]})]},i.id)),(!c||c.length===0)&&e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No quotes yet"})]})})]}),e.jsxs(a,{children:[e.jsxs(n,{className:"flex flex-row items-center justify-between",children:[e.jsx(d,{children:"Recent Users"}),e.jsx(l,{href:"/admin/users",children:e.jsx(r,{variant:"outline",size:"sm",children:"View All"})})]}),e.jsx(t,{children:e.jsxs("div",{className:"space-y-4",children:[x==null?void 0:x.map(i=>{var o;return e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:((o=i.name)==null?void 0:o.charAt(0))||"U"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:i.name}),e.jsx("p",{className:"text-xs text-gray-500",children:i.email}),e.jsxs("p",{className:"text-xs text-gray-500",children:["Joined ",new Date(i.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[i.role==="admin"&&e.jsx(m,{children:"Admin"}),e.jsx(m,{variant:"outline",children:i.email_verified_at?"Verified":"Unverified"})]})]},i.id)}),(!x||x.length===0)&&e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No users yet"})]})})]})]})]})]})}export{R as default};
