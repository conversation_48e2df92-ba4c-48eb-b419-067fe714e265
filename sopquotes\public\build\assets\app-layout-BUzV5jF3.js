import{r as i,j as n,a as dn,K as ge,$ as _}from"./app-BWHLaRS2.js";import{u as D,S as J,c as y,a as un,b as fn,B as X}from"./button-CUovRkQ3.js";import{d as I,a as P,e as F,c as ee,b as pn,u as Ve,P as U,f as hn}from"./index-BDjL_iy4.js";import{P as N,d as mn}from"./index-C3-VNe4Y.js";import{R as gn}from"./index-CB7we0-r.js";import{u as xn,o as vn,s as bn,f as yn,d as wn,e as jn,h as En,l as Cn,g as Nn,i as Pn,R as An,X as Tn,j as Sn,k as $e,m as _n,c as W,D as qe,a as Ye,b as Xe,F as Dn}from"./flash-message-BOB8jroJ.js";import{c as k,B as xe}from"./book-open-DxiZ0b6m.js";import{S as Rn}from"./settings-FGMHgWsK.js";import{C as On}from"./calendar-D56_txMz.js";import{C as In}from"./chevron-right-fzPpalsz.js";import{U as le}from"./user-CmroWLXK.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],kn=k("ChevronsUpDown",Ln);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mn=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],Ze=k("Folder",Mn);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fn=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],$n=k("House",Fn);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hn=[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]],Bn=k("LogIn",Hn);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wn=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],Qe=k("LogOut",Wn);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Un=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],zn=k("PanelLeft",Un);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gn=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],Kn=k("UserPlus",Gn);function Vn(e,t=globalThis==null?void 0:globalThis.document){const r=I(e);i.useEffect(()=>{const a=o=>{o.key==="Escape"&&r(o)};return t.addEventListener("keydown",a,{capture:!0}),()=>t.removeEventListener("keydown",a,{capture:!0})},[r,t])}var qn="DismissableLayer",pe="dismissableLayer.update",Yn="dismissableLayer.pointerDownOutside",Xn="dismissableLayer.focusOutside",He,Je=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ve=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:a,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:c,onDismiss:l,...d}=e,u=i.useContext(Je),[f,g]=i.useState(null),x=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=i.useState({}),b=D(t,E=>g(E)),p=Array.from(u.layers),[w]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),m=p.indexOf(w),v=f?p.indexOf(f):-1,C=u.layersWithOutsidePointerEventsDisabled.size>0,j=v>=m,A=Jn(E=>{const O=E.target,G=[...u.branches].some(H=>H.contains(O));!j||G||(o==null||o(E),c==null||c(E),E.defaultPrevented||l==null||l())},x),T=er(E=>{const O=E.target;[...u.branches].some(H=>H.contains(O))||(s==null||s(E),c==null||c(E),E.defaultPrevented||l==null||l())},x);return Vn(E=>{v===u.layers.size-1&&(a==null||a(E),!E.defaultPrevented&&l&&(E.preventDefault(),l()))},x),i.useEffect(()=>{if(f)return r&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(He=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),Be(),()=>{r&&u.layersWithOutsidePointerEventsDisabled.size===1&&(x.body.style.pointerEvents=He)}},[f,x,r,u]),i.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),Be())},[f,u]),i.useEffect(()=>{const E=()=>h({});return document.addEventListener(pe,E),()=>document.removeEventListener(pe,E)},[]),n.jsx(N.div,{...d,ref:b,style:{pointerEvents:C?j?"auto":"none":void 0,...e.style},onFocusCapture:P(e.onFocusCapture,T.onFocusCapture),onBlurCapture:P(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:P(e.onPointerDownCapture,A.onPointerDownCapture)})});ve.displayName=qn;var Zn="DismissableLayerBranch",Qn=i.forwardRef((e,t)=>{const r=i.useContext(Je),a=i.useRef(null),o=D(t,a);return i.useEffect(()=>{const s=a.current;if(s)return r.branches.add(s),()=>{r.branches.delete(s)}},[r.branches]),n.jsx(N.div,{...e,ref:o})});Qn.displayName=Zn;function Jn(e,t=globalThis==null?void 0:globalThis.document){const r=I(e),a=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=l=>{if(l.target&&!a.current){let d=function(){et(Yn,r,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=d,t.addEventListener("click",o.current,{once:!0})):d()}else t.removeEventListener("click",o.current);a.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>a.current=!0}}function er(e,t=globalThis==null?void 0:globalThis.document){const r=I(e),a=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!a.current&&et(Xn,r,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}function Be(){const e=new CustomEvent(pe);document.dispatchEvent(e)}function et(e,t,r,{discrete:a}){const o=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),a?mn(o,s):o.dispatchEvent(s)}var ce=0;function tr(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??We()),document.body.insertAdjacentElement("beforeend",e[1]??We()),ce++,()=>{ce===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ce--}},[])}function We(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var de="focusScope.autoFocusOnMount",ue="focusScope.autoFocusOnUnmount",Ue={bubbles:!1,cancelable:!0},nr="FocusScope",tt=i.forwardRef((e,t)=>{const{loop:r=!1,trapped:a=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...c}=e,[l,d]=i.useState(null),u=I(o),f=I(s),g=i.useRef(null),x=D(t,p=>d(p)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(a){let p=function(C){if(h.paused||!l)return;const j=C.target;l.contains(j)?g.current=j:R(g.current,{select:!0})},w=function(C){if(h.paused||!l)return;const j=C.relatedTarget;j!==null&&(l.contains(j)||R(g.current,{select:!0}))},m=function(C){if(document.activeElement===document.body)for(const A of C)A.removedNodes.length>0&&R(l)};document.addEventListener("focusin",p),document.addEventListener("focusout",w);const v=new MutationObserver(m);return l&&v.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",w),v.disconnect()}}},[a,l,h.paused]),i.useEffect(()=>{if(l){Ge.add(h);const p=document.activeElement;if(!l.contains(p)){const m=new CustomEvent(de,Ue);l.addEventListener(de,u),l.dispatchEvent(m),m.defaultPrevented||(rr(lr(nt(l)),{select:!0}),document.activeElement===p&&R(l))}return()=>{l.removeEventListener(de,u),setTimeout(()=>{const m=new CustomEvent(ue,Ue);l.addEventListener(ue,f),l.dispatchEvent(m),m.defaultPrevented||R(p??document.body,{select:!0}),l.removeEventListener(ue,f),Ge.remove(h)},0)}}},[l,u,f,h]);const b=i.useCallback(p=>{if(!r&&!a||h.paused)return;const w=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,m=document.activeElement;if(w&&m){const v=p.currentTarget,[C,j]=ar(v);C&&j?!p.shiftKey&&m===j?(p.preventDefault(),r&&R(C,{select:!0})):p.shiftKey&&m===C&&(p.preventDefault(),r&&R(j,{select:!0})):m===v&&p.preventDefault()}},[r,a,h.paused]);return n.jsx(N.div,{tabIndex:-1,...c,ref:x,onKeyDown:b})});tt.displayName=nr;function rr(e,{select:t=!1}={}){const r=document.activeElement;for(const a of e)if(R(a,{select:t}),document.activeElement!==r)return}function ar(e){const t=nt(e),r=ze(t,e),a=ze(t.reverse(),e);return[r,a]}function nt(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{const o=a.tagName==="INPUT"&&a.type==="hidden";return a.disabled||a.hidden||o?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function ze(e,t){for(const r of e)if(!or(r,{upTo:t}))return r}function or(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function sr(e){return e instanceof HTMLInputElement&&"select"in e}function R(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&sr(e)&&t&&e.select()}}var Ge=ir();function ir(){let e=[];return{add(t){const r=e[0];t!==r&&(r==null||r.pause()),e=Ke(e,t),e.unshift(t)},remove(t){var r;e=Ke(e,t),(r=e[0])==null||r.resume()}}}function Ke(e,t){const r=[...e],a=r.indexOf(t);return a!==-1&&r.splice(a,1),r}function lr(e){return e.filter(t=>t.tagName!=="A")}var cr=dn.useId||(()=>{}),dr=0;function Z(e){const[t,r]=i.useState(cr());return F(()=>{r(a=>a??String(dr++))},[e]),e||(t?`radix-${t}`:"")}var ur="Arrow",rt=i.forwardRef((e,t)=>{const{children:r,width:a=10,height:o=5,...s}=e;return n.jsx(N.svg,{...s,ref:t,width:a,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:n.jsx("polygon",{points:"0,0 30,0 15,10"})})});rt.displayName=ur;var fr=rt,be="Popper",[at,ot]=ee(be),[pr,st]=at(be),it=e=>{const{__scopePopper:t,children:r}=e,[a,o]=i.useState(null);return n.jsx(pr,{scope:t,anchor:a,onAnchorChange:o,children:r})};it.displayName=be;var lt="PopperAnchor",ct=i.forwardRef((e,t)=>{const{__scopePopper:r,virtualRef:a,...o}=e,s=st(lt,r),c=i.useRef(null),l=D(t,c);return i.useEffect(()=>{s.onAnchorChange((a==null?void 0:a.current)||c.current)}),a?null:n.jsx(N.div,{...o,ref:l})});ct.displayName=lt;var ye="PopperContent",[hr,mr]=at(ye),dt=i.forwardRef((e,t)=>{var Re,Oe,Ie,Le,ke,Me;const{__scopePopper:r,side:a="bottom",sideOffset:o=0,align:s="center",alignOffset:c=0,arrowPadding:l=0,avoidCollisions:d=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:g="partial",hideWhenDetached:x=!1,updatePositionStrategy:h="optimized",onPlaced:b,...p}=e,w=st(ye,r),[m,v]=i.useState(null),C=D(t,B=>v(B)),[j,A]=i.useState(null),T=pn(j),E=(T==null?void 0:T.width)??0,O=(T==null?void 0:T.height)??0,G=a+(s!=="center"?"-"+s:""),H=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},Se=Array.isArray(u)?u:[u],Xt=Se.length>0,K={padding:H,boundary:Se.filter(xr),altBoundary:Xt},{refs:Zt,floatingStyles:_e,placement:Qt,isPositioned:V,middlewareData:M}=xn({strategy:"fixed",placement:G,whileElementsMounted:(...B)=>Nn(...B,{animationFrame:h==="always"}),elements:{reference:w.anchor},middleware:[vn({mainAxis:o+O,alignmentAxis:c}),d&&bn({mainAxis:!0,crossAxis:!1,limiter:g==="partial"?Cn():void 0,...K}),d&&yn({...K}),wn({...K,apply:({elements:B,rects:Fe,availableWidth:on,availableHeight:sn})=>{const{width:ln,height:cn}=Fe.reference,Y=B.floating.style;Y.setProperty("--radix-popper-available-width",`${on}px`),Y.setProperty("--radix-popper-available-height",`${sn}px`),Y.setProperty("--radix-popper-anchor-width",`${ln}px`),Y.setProperty("--radix-popper-anchor-height",`${cn}px`)}}),j&&jn({element:j,padding:l}),vr({arrowWidth:E,arrowHeight:O}),x&&En({strategy:"referenceHidden",...K})]}),[De,Jt]=pt(Qt),q=I(b);F(()=>{V&&(q==null||q())},[V,q]);const en=(Re=M.arrow)==null?void 0:Re.x,tn=(Oe=M.arrow)==null?void 0:Oe.y,nn=((Ie=M.arrow)==null?void 0:Ie.centerOffset)!==0,[rn,an]=i.useState();return F(()=>{m&&an(window.getComputedStyle(m).zIndex)},[m]),n.jsx("div",{ref:Zt.setFloating,"data-radix-popper-content-wrapper":"",style:{..._e,transform:V?_e.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:rn,"--radix-popper-transform-origin":[(Le=M.transformOrigin)==null?void 0:Le.x,(ke=M.transformOrigin)==null?void 0:ke.y].join(" "),...((Me=M.hide)==null?void 0:Me.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:n.jsx(hr,{scope:r,placedSide:De,onArrowChange:A,arrowX:en,arrowY:tn,shouldHideArrow:nn,children:n.jsx(N.div,{"data-side":De,"data-align":Jt,...p,ref:C,style:{...p.style,animation:V?void 0:"none"}})})})});dt.displayName=ye;var ut="PopperArrow",gr={top:"bottom",right:"left",bottom:"top",left:"right"},ft=i.forwardRef(function(t,r){const{__scopePopper:a,...o}=t,s=mr(ut,a),c=gr[s.placedSide];return n.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:n.jsx(fr,{...o,ref:r,style:{...o.style,display:"block"}})})});ft.displayName=ut;function xr(e){return e!==null}var vr=e=>({name:"transformOrigin",options:e,fn(t){var w,m,v;const{placement:r,rects:a,middlewareData:o}=t,c=((w=o.arrow)==null?void 0:w.centerOffset)!==0,l=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[u,f]=pt(r),g={start:"0%",center:"50%",end:"100%"}[f],x=(((m=o.arrow)==null?void 0:m.x)??0)+l/2,h=(((v=o.arrow)==null?void 0:v.y)??0)+d/2;let b="",p="";return u==="bottom"?(b=c?g:`${x}px`,p=`${-d}px`):u==="top"?(b=c?g:`${x}px`,p=`${a.floating.height+d}px`):u==="right"?(b=`${-d}px`,p=c?g:`${h}px`):u==="left"&&(b=`${a.floating.width+d}px`,p=c?g:`${h}px`),{data:{x:b,y:p}}}});function pt(e){const[t,r="center"]=e.split("-");return[t,r]}var br=it,yr=ct,wr=dt,jr=ft,Er="Portal",we=i.forwardRef((e,t)=>{var l;const{container:r,...a}=e,[o,s]=i.useState(!1);F(()=>s(!0),[]);const c=r||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return c?gn.createPortal(n.jsx(N.div,{...a,ref:t}),c):null});we.displayName=Er;var Cr="VisuallyHidden",ht=i.forwardRef((e,t)=>n.jsx(N.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ht.displayName=Cr;var Nr=ht;const fe=768;function mt(){const[e,t]=i.useState();return i.useEffect(()=>{const r=window.matchMedia(`(max-width: ${fe-1}px)`),a=()=>{t(window.innerWidth<fe)};return r.addEventListener("change",a),t(window.innerWidth<fe),()=>r.removeEventListener("change",a)},[]),!!e}var je="Dialog",[gt,Eo]=ee(je),[Pr,S]=gt(je),xt=e=>{const{__scopeDialog:t,children:r,open:a,defaultOpen:o,onOpenChange:s,modal:c=!0}=e,l=i.useRef(null),d=i.useRef(null),[u=!1,f]=Ve({prop:a,defaultProp:o,onChange:s});return n.jsx(Pr,{scope:t,triggerRef:l,contentRef:d,contentId:Z(),titleId:Z(),descriptionId:Z(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(g=>!g),[f]),modal:c,children:r})};xt.displayName=je;var vt="DialogTrigger",Ar=i.forwardRef((e,t)=>{const{__scopeDialog:r,...a}=e,o=S(vt,r),s=D(t,o.triggerRef);return n.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Ne(o.open),...a,ref:s,onClick:P(e.onClick,o.onOpenToggle)})});Ar.displayName=vt;var Ee="DialogPortal",[Tr,bt]=gt(Ee,{forceMount:void 0}),yt=e=>{const{__scopeDialog:t,forceMount:r,children:a,container:o}=e,s=S(Ee,t);return n.jsx(Tr,{scope:t,forceMount:r,children:i.Children.map(a,c=>n.jsx(U,{present:r||s.open,children:n.jsx(we,{asChild:!0,container:o,children:c})}))})};yt.displayName=Ee;var Q="DialogOverlay",wt=i.forwardRef((e,t)=>{const r=bt(Q,e.__scopeDialog),{forceMount:a=r.forceMount,...o}=e,s=S(Q,e.__scopeDialog);return s.modal?n.jsx(U,{present:a||s.open,children:n.jsx(Sr,{...o,ref:t})}):null});wt.displayName=Q;var Sr=i.forwardRef((e,t)=>{const{__scopeDialog:r,...a}=e,o=S(Q,r);return n.jsx(An,{as:J,allowPinchZoom:!0,shards:[o.contentRef],children:n.jsx(N.div,{"data-state":Ne(o.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),L="DialogContent",jt=i.forwardRef((e,t)=>{const r=bt(L,e.__scopeDialog),{forceMount:a=r.forceMount,...o}=e,s=S(L,e.__scopeDialog);return n.jsx(U,{present:a||s.open,children:s.modal?n.jsx(_r,{...o,ref:t}):n.jsx(Dr,{...o,ref:t})})});jt.displayName=L;var _r=i.forwardRef((e,t)=>{const r=S(L,e.__scopeDialog),a=i.useRef(null),o=D(t,r.contentRef,a);return i.useEffect(()=>{const s=a.current;if(s)return Pn(s)},[]),n.jsx(Et,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:P(e.onCloseAutoFocus,s=>{var c;s.preventDefault(),(c=r.triggerRef.current)==null||c.focus()}),onPointerDownOutside:P(e.onPointerDownOutside,s=>{const c=s.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0;(c.button===2||l)&&s.preventDefault()}),onFocusOutside:P(e.onFocusOutside,s=>s.preventDefault())})}),Dr=i.forwardRef((e,t)=>{const r=S(L,e.__scopeDialog),a=i.useRef(!1),o=i.useRef(!1);return n.jsx(Et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var c,l;(c=e.onCloseAutoFocus)==null||c.call(e,s),s.defaultPrevented||(a.current||(l=r.triggerRef.current)==null||l.focus(),s.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:s=>{var d,u;(d=e.onInteractOutside)==null||d.call(e,s),s.defaultPrevented||(a.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=s.target;((u=r.triggerRef.current)==null?void 0:u.contains(c))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Et=i.forwardRef((e,t)=>{const{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:s,...c}=e,l=S(L,r),d=i.useRef(null),u=D(t,d);return tr(),n.jsxs(n.Fragment,{children:[n.jsx(tt,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:s,children:n.jsx(ve,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":Ne(l.open),...c,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),n.jsxs(n.Fragment,{children:[n.jsx(Rr,{titleId:l.titleId}),n.jsx(Ir,{contentRef:d,descriptionId:l.descriptionId})]})]})}),Ce="DialogTitle",Ct=i.forwardRef((e,t)=>{const{__scopeDialog:r,...a}=e,o=S(Ce,r);return n.jsx(N.h2,{id:o.titleId,...a,ref:t})});Ct.displayName=Ce;var Nt="DialogDescription",Pt=i.forwardRef((e,t)=>{const{__scopeDialog:r,...a}=e,o=S(Nt,r);return n.jsx(N.p,{id:o.descriptionId,...a,ref:t})});Pt.displayName=Nt;var At="DialogClose",Tt=i.forwardRef((e,t)=>{const{__scopeDialog:r,...a}=e,o=S(At,r);return n.jsx(N.button,{type:"button",...a,ref:t,onClick:P(e.onClick,()=>o.onOpenChange(!1))})});Tt.displayName=At;function Ne(e){return e?"open":"closed"}var St="DialogTitleWarning",[Co,_t]=hn(St,{contentName:L,titleName:Ce,docsSlug:"dialog"}),Rr=({titleId:e})=>{const t=_t(St),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Or="DialogDescriptionWarning",Ir=({contentRef:e,descriptionId:t})=>{const a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${_t(Or).contentName}}.`;return i.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},Lr=xt,kr=yt,Mr=wt,Fr=jt,$r=Ct,Hr=Pt,Br=Tt;function Wr({...e}){return n.jsx(Lr,{"data-slot":"sheet",...e})}function Ur({...e}){return n.jsx(kr,{"data-slot":"sheet-portal",...e})}function zr({className:e,...t}){return n.jsx(Mr,{"data-slot":"sheet-overlay",className:y("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function Gr({className:e,children:t,side:r="right",...a}){return n.jsxs(Ur,{children:[n.jsx(zr,{}),n.jsxs(Fr,{"data-slot":"sheet-content",className:y("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",r==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",r==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",r==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",r==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,n.jsxs(Br,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[n.jsx(Tn,{className:"size-4"}),n.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Kr({className:e,...t}){return n.jsx("div",{"data-slot":"sheet-header",className:y("flex flex-col gap-1.5 p-4",e),...t})}function Vr({className:e,...t}){return n.jsx($r,{"data-slot":"sheet-title",className:y("text-foreground font-semibold",e),...t})}function qr({className:e,...t}){return n.jsx(Hr,{"data-slot":"sheet-description",className:y("text-muted-foreground text-sm",e),...t})}var[te,No]=ee("Tooltip",[ot]),ne=ot(),Dt="TooltipProvider",Yr=700,he="tooltip.open",[Xr,Pe]=te(Dt),Rt=e=>{const{__scopeTooltip:t,delayDuration:r=Yr,skipDelayDuration:a=300,disableHoverableContent:o=!1,children:s}=e,[c,l]=i.useState(!0),d=i.useRef(!1),u=i.useRef(0);return i.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),n.jsx(Xr,{scope:t,isOpenDelayed:c,delayDuration:r,onOpen:i.useCallback(()=>{window.clearTimeout(u.current),l(!1)},[]),onClose:i.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l(!0),a)},[a]),isPointerInTransitRef:d,onPointerInTransitChange:i.useCallback(f=>{d.current=f},[]),disableHoverableContent:o,children:s})};Rt.displayName=Dt;var re="Tooltip",[Zr,z]=te(re),Ot=e=>{const{__scopeTooltip:t,children:r,open:a,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:c,delayDuration:l}=e,d=Pe(re,e.__scopeTooltip),u=ne(t),[f,g]=i.useState(null),x=Z(),h=i.useRef(0),b=c??d.disableHoverableContent,p=l??d.delayDuration,w=i.useRef(!1),[m=!1,v]=Ve({prop:a,defaultProp:o,onChange:E=>{E?(d.onOpen(),document.dispatchEvent(new CustomEvent(he))):d.onClose(),s==null||s(E)}}),C=i.useMemo(()=>m?w.current?"delayed-open":"instant-open":"closed",[m]),j=i.useCallback(()=>{window.clearTimeout(h.current),h.current=0,w.current=!1,v(!0)},[v]),A=i.useCallback(()=>{window.clearTimeout(h.current),h.current=0,v(!1)},[v]),T=i.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{w.current=!0,v(!0),h.current=0},p)},[p,v]);return i.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),n.jsx(br,{...u,children:n.jsx(Zr,{scope:t,contentId:x,open:m,stateAttribute:C,trigger:f,onTriggerChange:g,onTriggerEnter:i.useCallback(()=>{d.isOpenDelayed?T():j()},[d.isOpenDelayed,T,j]),onTriggerLeave:i.useCallback(()=>{b?A():(window.clearTimeout(h.current),h.current=0)},[A,b]),onOpen:j,onClose:A,disableHoverableContent:b,children:r})})};Ot.displayName=re;var me="TooltipTrigger",It=i.forwardRef((e,t)=>{const{__scopeTooltip:r,...a}=e,o=z(me,r),s=Pe(me,r),c=ne(r),l=i.useRef(null),d=D(t,l,o.onTriggerChange),u=i.useRef(!1),f=i.useRef(!1),g=i.useCallback(()=>u.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),n.jsx(yr,{asChild:!0,...c,children:n.jsx(N.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...a,ref:d,onPointerMove:P(e.onPointerMove,x=>{x.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:P(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:P(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:P(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:P(e.onBlur,o.onClose),onClick:P(e.onClick,o.onClose)})})});It.displayName=me;var Ae="TooltipPortal",[Qr,Jr]=te(Ae,{forceMount:void 0}),Lt=e=>{const{__scopeTooltip:t,forceMount:r,children:a,container:o}=e,s=z(Ae,t);return n.jsx(Qr,{scope:t,forceMount:r,children:n.jsx(U,{present:r||s.open,children:n.jsx(we,{asChild:!0,container:o,children:a})})})};Lt.displayName=Ae;var $="TooltipContent",kt=i.forwardRef((e,t)=>{const r=Jr($,e.__scopeTooltip),{forceMount:a=r.forceMount,side:o="top",...s}=e,c=z($,e.__scopeTooltip);return n.jsx(U,{present:a||c.open,children:c.disableHoverableContent?n.jsx(Mt,{side:o,...s,ref:t}):n.jsx(ea,{side:o,...s,ref:t})})}),ea=i.forwardRef((e,t)=>{const r=z($,e.__scopeTooltip),a=Pe($,e.__scopeTooltip),o=i.useRef(null),s=D(t,o),[c,l]=i.useState(null),{trigger:d,onClose:u}=r,f=o.current,{onPointerInTransitChange:g}=a,x=i.useCallback(()=>{l(null),g(!1)},[g]),h=i.useCallback((b,p)=>{const w=b.currentTarget,m={x:b.clientX,y:b.clientY},v=ra(m,w.getBoundingClientRect()),C=aa(m,v),j=oa(p.getBoundingClientRect()),A=ia([...C,...j]);l(A),g(!0)},[g]);return i.useEffect(()=>()=>x(),[x]),i.useEffect(()=>{if(d&&f){const b=w=>h(w,f),p=w=>h(w,d);return d.addEventListener("pointerleave",b),f.addEventListener("pointerleave",p),()=>{d.removeEventListener("pointerleave",b),f.removeEventListener("pointerleave",p)}}},[d,f,h,x]),i.useEffect(()=>{if(c){const b=p=>{const w=p.target,m={x:p.clientX,y:p.clientY},v=(d==null?void 0:d.contains(w))||(f==null?void 0:f.contains(w)),C=!sa(m,c);v?x():C&&(x(),u())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[d,f,c,u,x]),n.jsx(Mt,{...e,ref:s})}),[ta,na]=te(re,{isInside:!1}),Mt=i.forwardRef((e,t)=>{const{__scopeTooltip:r,children:a,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:c,...l}=e,d=z($,r),u=ne(r),{onClose:f}=d;return i.useEffect(()=>(document.addEventListener(he,f),()=>document.removeEventListener(he,f)),[f]),i.useEffect(()=>{if(d.trigger){const g=x=>{const h=x.target;h!=null&&h.contains(d.trigger)&&f()};return window.addEventListener("scroll",g,{capture:!0}),()=>window.removeEventListener("scroll",g,{capture:!0})}},[d.trigger,f]),n.jsx(ve,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:g=>g.preventDefault(),onDismiss:f,children:n.jsxs(wr,{"data-state":d.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[n.jsx(un,{children:a}),n.jsx(ta,{scope:r,isInside:!0,children:n.jsx(Nr,{id:d.contentId,role:"tooltip",children:o||a})})]})})});kt.displayName=$;var Ft="TooltipArrow",$t=i.forwardRef((e,t)=>{const{__scopeTooltip:r,...a}=e,o=ne(r);return na(Ft,r).isInside?null:n.jsx(jr,{...o,...a,ref:t})});$t.displayName=Ft;function ra(e,t){const r=Math.abs(t.top-e.y),a=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(r,a,o,s)){case s:return"left";case o:return"right";case r:return"top";case a:return"bottom";default:throw new Error("unreachable")}}function aa(e,t,r=5){const a=[];switch(t){case"top":a.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":a.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":a.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":a.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return a}function oa(e){const{top:t,right:r,bottom:a,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:a},{x:o,y:a}]}function sa(e,t){const{x:r,y:a}=e;let o=!1;for(let s=0,c=t.length-1;s<t.length;c=s++){const l=t[s].x,d=t[s].y,u=t[c].x,f=t[c].y;d>a!=f>a&&r<(u-l)*(a-d)/(f-d)+l&&(o=!o)}return o}function ia(e){const t=e.slice();return t.sort((r,a)=>r.x<a.x?-1:r.x>a.x?1:r.y<a.y?-1:r.y>a.y?1:0),la(t)}function la(e){if(e.length<=1)return e.slice();const t=[];for(let a=0;a<e.length;a++){const o=e[a];for(;t.length>=2;){const s=t[t.length-1],c=t[t.length-2];if((s.x-c.x)*(o.y-c.y)>=(s.y-c.y)*(o.x-c.x))t.pop();else break}t.push(o)}t.pop();const r=[];for(let a=e.length-1;a>=0;a--){const o=e[a];for(;r.length>=2;){const s=r[r.length-1],c=r[r.length-2];if((s.x-c.x)*(o.y-c.y)>=(s.y-c.y)*(o.x-c.x))r.pop();else break}r.push(o)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var ca=Rt,da=Ot,ua=It,fa=Lt,pa=kt,ha=$t;function Ht({delayDuration:e=0,...t}){return n.jsx(ca,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function ma({...e}){return n.jsx(Ht,{children:n.jsx(da,{"data-slot":"tooltip",...e})})}function ga({...e}){return n.jsx(ua,{"data-slot":"tooltip-trigger",...e})}function xa({className:e,sideOffset:t=4,children:r,...a}){return n.jsx(fa,{children:n.jsxs(pa,{"data-slot":"tooltip-content",sideOffset:t,className:y("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...a,children:[r,n.jsx(ha,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const va="sidebar_state",ba=60*60*24*7,ya="16rem",wa="18rem",ja="3rem",Ea="b",Bt=i.createContext(null);function ae(){const e=i.useContext(Bt);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Ca({defaultOpen:e=!0,open:t,onOpenChange:r,className:a,style:o,children:s,...c}){const l=mt(),[d,u]=i.useState(!1),[f,g]=i.useState(e),x=t??f,h=i.useCallback(m=>{const v=typeof m=="function"?m(x):m;r?r(v):g(v),document.cookie=`${va}=${v}; path=/; max-age=${ba}`},[r,x]),b=i.useCallback(()=>l?u(m=>!m):h(m=>!m),[l,h,u]);i.useEffect(()=>{const m=v=>{v.key===Ea&&(v.metaKey||v.ctrlKey)&&(v.preventDefault(),b())};return window.addEventListener("keydown",m),()=>window.removeEventListener("keydown",m)},[b]);const p=x?"expanded":"collapsed",w=i.useMemo(()=>({state:p,open:x,setOpen:h,isMobile:l,openMobile:d,setOpenMobile:u,toggleSidebar:b}),[p,x,h,l,d,u,b]);return n.jsx(Bt.Provider,{value:w,children:n.jsx(Ht,{delayDuration:0,children:n.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":ya,"--sidebar-width-icon":ja,...o},className:y("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",a),...c,children:s})})})}function Na({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:a,children:o,...s}){const{isMobile:c,state:l,openMobile:d,setOpenMobile:u}=ae();return r==="none"?n.jsx("div",{"data-slot":"sidebar",className:y("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",a),...s,children:o}):c?n.jsxs(Wr,{open:d,onOpenChange:u,...s,children:[n.jsxs(Kr,{className:"sr-only",children:[n.jsx(Vr,{children:"Sidebar"}),n.jsx(qr,{children:"Displays the mobile sidebar."})]}),n.jsx(Gr,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":wa},side:e,children:n.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):n.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":l==="collapsed"?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[n.jsx("div",{className:y("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),n.jsx("div",{className:y("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",a),...s,children:n.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function Pa({className:e,onClick:t,...r}){const{toggleSidebar:a}=ae();return n.jsxs(X,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:y("h-7 w-7",e),onClick:o=>{t==null||t(o),a()},...r,children:[n.jsx(zn,{}),n.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Aa({className:e,...t}){return n.jsx("main",{"data-slot":"sidebar-inset",className:y("bg-background relative flex min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function Ta({className:e,...t}){return n.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:y("flex flex-col gap-2 p-2",e),...t})}function Sa({className:e,...t}){return n.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:y("flex flex-col gap-2 p-2",e),...t})}function _a({className:e,...t}){return n.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:y("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function Wt({className:e,...t}){return n.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:y("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Da({className:e,asChild:t=!1,...r}){const a=t?J:"div";return n.jsx(a,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:y("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function Ra({className:e,...t}){return n.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:y("w-full text-sm",e),...t})}function oe({className:e,...t}){return n.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:y("flex w-full min-w-0 flex-col gap-1",e),...t})}function se({className:e,...t}){return n.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:y("group/menu-item relative",e),...t})}const Oa=fn("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function ie({asChild:e=!1,isActive:t=!1,variant:r="default",size:a="default",tooltip:o,className:s,...c}){const l=e?J:"button",{isMobile:d,state:u}=ae(),f=n.jsx(l,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":a,"data-active":t,className:y(Oa({variant:r,size:a}),s),...c});return o?(typeof o=="string"&&(o={children:o}),n.jsxs(ma,{children:[n.jsx(ga,{asChild:!0,children:f}),n.jsx(xa,{side:"right",align:"center",hidden:u!=="collapsed"||d,...o})]})):f}function Ia({variant:e="header",children:t,...r}){return e==="sidebar"?n.jsx(Aa,{...r,children:t}):n.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...r,children:t})}function La({children:e,variant:t="header"}){const[r,a]=i.useState(()=>typeof window<"u"?localStorage.getItem("sidebar")!=="false":!0),o=s=>{a(s),typeof window<"u"&&localStorage.setItem("sidebar",String(s))};return t==="header"?n.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):n.jsx(Ca,{defaultOpen:r,open:r,onOpenChange:o,children:e})}function ka({iconNode:e,className:t,...r}){return n.jsx(e,{className:y("h-4 w-4",t),...r})}function Ma({items:e,className:t,...r}){return n.jsx(Wt,{...r,className:`group-data-[collapsible=icon]:p-0 ${t||""}`,children:n.jsx(Ra,{children:n.jsx(oe,{children:e.map(a=>n.jsx(se,{children:n.jsx(ie,{asChild:!0,className:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100",children:n.jsxs("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",children:[a.icon&&n.jsx(ka,{iconNode:a.icon,className:"h-5 w-5"}),n.jsx("span",{children:a.title})]})})},a.title))})})})}function Fa({items:e=[]}){const t=ge();return n.jsxs(Wt,{className:"px-2 py-0",children:[n.jsx(Da,{children:"Platform"}),n.jsx(oe,{children:e.map(r=>n.jsx(se,{children:n.jsx(ie,{asChild:!0,isActive:r.url===t.url,children:n.jsxs(_,{href:r.url,prefetch:!0,children:[r.icon&&n.jsx(r.icon,{}),n.jsx("span",{children:r.title})]})})},r.title))})]})}var Te="Avatar",[$a,Po]=ee(Te),[Ha,Ut]=$a(Te),zt=i.forwardRef((e,t)=>{const{__scopeAvatar:r,...a}=e,[o,s]=i.useState("idle");return n.jsx(Ha,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:n.jsx(N.span,{...a,ref:t})})});zt.displayName=Te;var Gt="AvatarImage",Kt=i.forwardRef((e,t)=>{const{__scopeAvatar:r,src:a,onLoadingStatusChange:o=()=>{},...s}=e,c=Ut(Gt,r),l=Ba(a,s.referrerPolicy),d=I(u=>{o(u),c.onImageLoadingStatusChange(u)});return F(()=>{l!=="idle"&&d(l)},[l,d]),l==="loaded"?n.jsx(N.img,{...s,ref:t,src:a}):null});Kt.displayName=Gt;var Vt="AvatarFallback",qt=i.forwardRef((e,t)=>{const{__scopeAvatar:r,delayMs:a,...o}=e,s=Ut(Vt,r),[c,l]=i.useState(a===void 0);return i.useEffect(()=>{if(a!==void 0){const d=window.setTimeout(()=>l(!0),a);return()=>window.clearTimeout(d)}},[a]),c&&s.imageLoadingStatus!=="loaded"?n.jsx(N.span,{...o,ref:t}):null});qt.displayName=Vt;function Ba(e,t){const[r,a]=i.useState("idle");return F(()=>{if(!e){a("error");return}let o=!0;const s=new window.Image,c=l=>()=>{o&&a(l)};return a("loading"),s.onload=c("loaded"),s.onerror=c("error"),s.src=e,t&&(s.referrerPolicy=t),()=>{o=!1}},[e,t]),r}var Wa=zt,Ua=Kt,za=qt;function Ga({className:e,...t}){return n.jsx(Wa,{"data-slot":"avatar",className:y("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Ka({className:e,...t}){return n.jsx(Ua,{"data-slot":"avatar-image",className:y("aspect-square size-full",e),...t})}function Va({className:e,...t}){return n.jsx(za,{"data-slot":"avatar-fallback",className:y("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function qa(){return i.useCallback(t=>{const r=t.trim().split(" ");if(r.length===0)return"";if(r.length===1)return r[0].charAt(0).toUpperCase();const a=r[0].charAt(0),o=r[r.length-1].charAt(0);return`${a}${o}`.toUpperCase()},[])}function Yt({user:e,showEmail:t=!1}){const r=qa();return n.jsxs(n.Fragment,{children:[n.jsxs(Ga,{className:"h-8 w-8 overflow-hidden rounded-full",children:[n.jsx(Ka,{src:e.avatar,alt:e.name}),n.jsx(Va,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:r(e.name)})]}),n.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[n.jsx("span",{className:"truncate font-medium",children:e.name}),t&&n.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}function Ya(){return i.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function Xa({user:e}){const t=Ya();return n.jsxs(n.Fragment,{children:[n.jsx(Sn,{className:"p-0 font-normal",children:n.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:n.jsx(Yt,{user:e,showEmail:!0})})}),n.jsx($e,{}),n.jsx(_n,{children:n.jsx(W,{asChild:!0,children:n.jsxs(_,{className:"block w-full",href:route("profile.edit"),as:"button",prefetch:!0,onClick:t,children:[n.jsx(Rn,{className:"mr-2"}),"Settings"]})})}),n.jsx($e,{}),n.jsx(W,{asChild:!0,children:n.jsxs(_,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:t,children:[n.jsx(Qe,{className:"mr-2"}),"Log out"]})})]})}function Za(){const{auth:e}=ge().props,{state:t}=ae(),r=mt();return n.jsx(oe,{children:n.jsx(se,{children:n.jsxs(qe,{children:[n.jsx(Ye,{asChild:!0,children:n.jsxs(ie,{size:"lg",className:"text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent group",children:[n.jsx(Yt,{user:e.user}),n.jsx(kn,{className:"ml-auto size-4"})]})}),n.jsx(Xe,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:r?"bottom":t==="collapsed"?"left":"bottom",children:n.jsx(Xa,{user:e.user})})]})})})}function Qa(){return n.jsxs(n.Fragment,{children:[n.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600",children:n.jsx(xe,{className:"h-5 w-5 text-white"})}),n.jsxs("div",{className:"ml-1 grid flex-1 text-left text-sm",children:[n.jsx("span",{className:"mb-0.5 truncate leading-none font-semibold",children:"SOP Quotes"}),n.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Spirit of Prophecy"})]})]})}const Ja=[{title:"Home",url:"/",icon:$n},{title:"All Quotes",url:"/quotes",icon:xe},{title:"Daily Quote",url:"/quotes/daily",icon:On},{title:"Categories",url:"/quotes?view=categories",icon:Ze}],eo=[{title:"Repository",url:"https://github.com/laravel/react-starter-kit",icon:Ze},{title:"Documentation",url:"https://laravel.com/docs/starter-kits",icon:xe}];function to(){return n.jsxs(Na,{collapsible:"icon",variant:"inset",children:[n.jsx(Ta,{children:n.jsx(oe,{children:n.jsx(se,{children:n.jsx(ie,{size:"lg",asChild:!0,children:n.jsx(_,{href:"/",prefetch:!0,children:n.jsx(Qa,{})})})})})}),n.jsx(_a,{children:n.jsx(Fa,{items:Ja})}),n.jsxs(Sa,{children:[n.jsx(Ma,{items:eo,className:"mt-auto"}),n.jsx(Za,{})]})]})}function no({...e}){return n.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function ro({className:e,...t}){return n.jsx("ol",{"data-slot":"breadcrumb-list",className:y("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function ao({className:e,...t}){return n.jsx("li",{"data-slot":"breadcrumb-item",className:y("inline-flex items-center gap-1.5",e),...t})}function oo({asChild:e,className:t,...r}){const a=e?J:"a";return n.jsx(a,{"data-slot":"breadcrumb-link",className:y("hover:text-foreground transition-colors",t),...r})}function so({className:e,...t}){return n.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:y("text-foreground font-normal",e),...t})}function io({children:e,className:t,...r}){return n.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:y("[&>svg]:size-3.5",t),...r,children:e??n.jsx(In,{})})}function lo({breadcrumbs:e}){return n.jsx(n.Fragment,{children:e.length>0&&n.jsx(no,{children:n.jsx(ro,{children:e.map((t,r)=>{const a=r===e.length-1;return n.jsxs(i.Fragment,{children:[n.jsx(ao,{children:a?n.jsx(so,{children:t.title}):n.jsx(oo,{asChild:!0,children:n.jsx(_,{href:t.href,children:t.title})})}),!a&&n.jsx(io,{})]},r)})})})})}function co({breadcrumbs:e=[]}){const{auth:t}=ge().props;return n.jsxs("header",{className:"border-sidebar-border/50 flex h-16 shrink-0 items-center gap-2 border-b px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:[n.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[n.jsx(Pa,{className:"-ml-1"}),n.jsx(lo,{breadcrumbs:e})]}),n.jsx("div",{className:"flex items-center gap-2",children:t.user?n.jsxs(qe,{children:[n.jsx(Ye,{asChild:!0,children:n.jsxs(X,{variant:"ghost",size:"sm",className:"flex items-center gap-2",children:[n.jsx(le,{className:"h-4 w-4"}),n.jsx("span",{className:"hidden sm:inline",children:t.user.name})]})}),n.jsxs(Xe,{align:"end",children:[n.jsx(W,{asChild:!0,children:n.jsxs(_,{href:"/dashboard",className:"flex items-center gap-2",children:[n.jsx(le,{className:"h-4 w-4"}),"My Dashboard"]})}),n.jsx(W,{asChild:!0,children:n.jsxs(_,{href:"/profile",className:"flex items-center gap-2",children:[n.jsx(le,{className:"h-4 w-4"}),"Profile"]})}),n.jsx(W,{asChild:!0,children:n.jsxs(_,{href:"/logout",method:"post",className:"flex items-center gap-2",children:[n.jsx(Qe,{className:"h-4 w-4"}),"Logout"]})})]})]}):n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx(_,{href:"/login",children:n.jsxs(X,{variant:"ghost",size:"sm",className:"flex items-center gap-2",children:[n.jsx(Bn,{className:"h-4 w-4"}),n.jsx("span",{className:"hidden sm:inline",children:"Login"})]})}),n.jsx(_,{href:"/register",children:n.jsxs(X,{size:"sm",className:"flex items-center gap-2",children:[n.jsx(Kn,{className:"h-4 w-4"}),n.jsx("span",{className:"hidden sm:inline",children:"Register"})]})})]})})]})}function uo({children:e,breadcrumbs:t=[]}){return n.jsxs(La,{variant:"sidebar",children:[n.jsx(to,{}),n.jsxs(Ia,{variant:"sidebar",children:[n.jsx(co,{breadcrumbs:t}),e]}),n.jsx(Dn,{})]})}const Ao=({children:e,breadcrumbs:t,...r})=>n.jsx(uo,{breadcrumbs:t,...r,children:e});export{Ao as A};
