import { Head, Link, useForm, router } from '@inertiajs/react';
import { ArrowLeft, Save, Tag, Trash2, BookOpen } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

export default function AdminCategoryEdit({ category, auth }) {
    const { data, setData, put, processing, errors, reset } = useForm({
        name: category.name || '',
        description: category.description || '',
        is_active: category.is_active || false,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route('admin.categories.update', category.id));
    };

    const handleDelete = () => {
        if (category.quotes && category.quotes.length > 0) {
            alert(`Cannot delete "${category.name}" because it has ${category.quotes.length} quotes. Please move or delete the quotes first.`);
            return;
        }

        if (confirm(`Are you sure you want to delete the category "${category.name}"? This action cannot be undone.`)) {
            router.delete(route('admin.categories.destroy', category.id));
        }
    };

    return (
        <AdminLayout>
            <Head title={`Edit ${category.name} - Admin Dashboard`} />

            <div className="max-w-2xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>

                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Update category information and settings
                        </p>
                    </div>
                    <div className="flex space-x-3">
                        <Link href="/admin/categories">
                            <Button variant="outline">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Categories
                            </Button>
                        </Link>
                        <Button variant="destructive" onClick={handleDelete}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Category
                        </Button>
                    </div>
                </div>

                {/* Category Stats */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <BookOpen className="h-5 w-5" />
                            <span>Category Statistics</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div className="text-2xl font-bold text-blue-600">
                                    {category.quotes ? category.quotes.length : 0}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">Quotes</div>
                            </div>
                            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div className="text-2xl font-bold text-green-600">
                                    {category.is_active ? 'Active' : 'Inactive'}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">Status</div>
                            </div>
                            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <div className="text-2xl font-bold text-purple-600">
                                    {category.slug}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">URL Slug</div>
                            </div>
                        </div>

                        {category.quotes && category.quotes.length > 0 && (
                            <div className="mt-4">
                                <Link href={`/quotes?category=${category.slug}`}>
                                    <Button variant="outline" size="sm">
                                        <BookOpen className="h-4 w-4 mr-2" />
                                        View All Quotes in This Category
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Edit Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Tag className="h-5 w-5" />
                            <span>Category Information</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Category Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Category Name *</Label>
                                <Input
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., Faith, Hope, Love"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                                <p className="text-sm text-gray-500">
                                    Changing the name will update the URL slug automatically.
                                </p>
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Describe what types of quotes belong in this category..."
                                    rows={3}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Active Category</Label>
                            </div>
                            <p className="text-sm text-gray-500 ml-6">
                                Inactive categories are hidden from users and cannot be assigned to new quotes.
                            </p>

                            {/* Submit Buttons */}
                            <div className="flex justify-end space-x-4 pt-6">
                                <Button type="button" variant="outline" onClick={() => reset()}>
                                    Reset Changes
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? (
                                        <>
                                            <Save className="h-4 w-4 mr-2 animate-spin" />
                                            Updating...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4 mr-2" />
                                            Update Category
                                        </>
                                    )}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Danger Zone */}
                {category.quotes && category.quotes.length > 0 && (
                    <Card className="border-red-200 dark:border-red-800">
                        <CardHeader>
                            <CardTitle className="text-red-600 dark:text-red-400">Danger Zone</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                                <p className="text-sm text-red-800 dark:text-red-200 mb-3">
                                    This category cannot be deleted because it contains {category.quotes.length} quotes.
                                    You must first move or delete all quotes in this category before it can be deleted.
                                </p>
                                <Badge variant="destructive">
                                    {category.quotes.length} quotes must be handled first
                                </Badge>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AdminLayout>
    );
}
