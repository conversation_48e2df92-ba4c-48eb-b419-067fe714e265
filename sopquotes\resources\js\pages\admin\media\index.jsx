import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Search, Upload, FileText, Volume2, Download, Trash2, Eye, Edit, ToggleLeft, ToggleRight } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';


export default function AdminMediaIndex({ media = { data: [], total: 0, links: [] }, categories = [], filters = {}, auth = {} }) {
    // Ensure filters is an object, not an array
    const safeFilters = Array.isArray(filters) ? {} : filters;

    const [searchTerm, setSearchTerm] = useState(String(safeFilters.search || ''));
    const [fileTypeFilter, setFileTypeFilter] = useState(String(safeFilters.file_type || ''));
    const [categoryFilter, setCategoryFilter] = useState(String(safeFilters.category || ''));
    const [statusFilter, setStatusFilter] = useState(String(safeFilters.status || ''));



    const handleSearch = (e) => {
        e.preventDefault();
        router.get('/admin/media', {
            search: searchTerm,
            file_type: fileTypeFilter,
            category: categoryFilter,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setFileTypeFilter('');
        setCategoryFilter('');
        setStatusFilter('');
        router.get('/admin/media', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDelete = (mediaFile) => {
        if (confirm(`Are you sure you want to delete "${mediaFile.title}"? This action cannot be undone.`)) {
            router.delete(`/admin/media/${mediaFile.id}`, {
                preserveScroll: true,
            });
        }
    };

    const toggleFeatured = (mediaFile) => {
        router.patch(`/admin/media/${mediaFile.id}/toggle-featured`, {}, {
            preserveScroll: true,
        });
    };

    const toggleStatus = (mediaFile) => {
        router.patch(`/admin/media/${mediaFile.id}/toggle-status`, {}, {
            preserveScroll: true,
        });
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const getFileIcon = (type) => {
        switch (type) {
            case 'pdf':
                return <FileText className="h-8 w-8 text-red-500" />;
            case 'audio':
                return <Volume2 className="h-8 w-8 text-blue-500" />;
            default:
                return <FileText className="h-8 w-8 text-gray-500" />;
        }
    };

    const formatDate = (date) => {
        return new Date(date).toLocaleDateString();
    };

    return (
        <AdminLayout>
            <Head title="Media Gallery - Admin Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Media Gallery</h1>
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Manage PDF and audio files attached to quotes
                        </p>
                    </div>
                    <Button>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Files
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Files</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{mediaFiles.length}</div>
                            <p className="text-xs text-muted-foreground">
                                Across all quotes
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">PDF Files</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {mediaFiles.filter(f => f.type === 'pdf').length}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Document files
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Audio Files</CardTitle>
                            <Volume2 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {mediaFiles.filter(f => f.type === 'audio').length}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Audio recordings
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
                            <Download className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {mediaFiles.reduce((sum, file) => sum + file.downloads, 0)}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                All time downloads
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <Input
                                        type="text"
                                        placeholder="Search files..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <select
                                        value={fileTypeFilter}
                                        onChange={(e) => setFileTypeFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All File Types</option>
                                        <option value="pdf">PDF Files</option>
                                        <option value="audio">Audio Files</option>
                                    </select>
                                </div>

                                <div>
                                    <select
                                        value={categoryFilter}
                                        onChange={(e) => setCategoryFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Categories</option>
                                        {Array.isArray(categories) && categories.map(category => (
                                            <option key={category.id} value={category.id.toString()}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <select
                                        value={statusFilter}
                                        onChange={(e) => setStatusFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>

                                <div className="flex space-x-2">
                                    <Button type="submit">
                                        <Search className="h-4 w-4 mr-2" />
                                        Search
                                    </Button>
                                    <Button type="button" variant="outline" onClick={clearFilters}>
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Files List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Media Files</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {media?.data?.length > 0 ? media.data.map((file) => (
                                <div key={file.id} className="border rounded-lg p-6">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-start space-x-4">
                                            <div className="flex-shrink-0">
                                                {getFileIcon(file.file_type)}
                                            </div>

                                            <div className="flex-1">
                                                <div className="flex items-center space-x-2 mb-2">
                                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                        {file.title}
                                                    </h3>
                                                    <Badge variant={file.file_type === 'pdf' ? 'destructive' : 'default'}>
                                                        {file.file_type.toUpperCase()}
                                                    </Badge>
                                                    {file.is_featured && (
                                                        <Badge variant="secondary">Featured</Badge>
                                                    )}
                                                    <Badge variant={file.is_active ? 'default' : 'outline'}>
                                                        {file.is_active ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </div>

                                                {file.description && (
                                                    <p className="text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                                                        {file.description}
                                                    </p>
                                                )}

                                                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <span>Size: {formatFileSize(file.file_size)}</span>
                                                    <span>Views: {file.view_count || 0}</span>
                                                    <span>Uploaded: {formatDate(file.created_at)}</span>
                                                    {file.category && (
                                                        <span>Category: {file.category.name}</span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex flex-col space-y-2 ml-4">
                                            <Link href={`/admin/media/${file.id}`}>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    View
                                                </Button>
                                            </Link>

                                            <Link href={`/admin/media/${file.id}/edit`}>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    Edit
                                                </Button>
                                            </Link>

                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="w-full"
                                                onClick={() => toggleFeatured(file)}
                                            >
                                                {file.is_featured ? 'Unfeature' : 'Feature'}
                                            </Button>

                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="w-full"
                                                onClick={() => toggleStatus(file)}
                                            >
                                                {file.is_active ? 'Deactivate' : 'Activate'}
                                            </Button>

                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                className="w-full"
                                                onClick={() => handleDelete(file)}
                                            >
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-12">
                                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-500 text-lg">No media files found</p>
                                    <p className="text-gray-400 text-sm mt-2">
                                        Upload files through the quote management interface or create new media files
                                    </p>
                                    <Link href="/admin/media/create">
                                        <Button className="mt-4">
                                            <Upload className="h-4 w-4 mr-2" />
                                            Upload Media
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
