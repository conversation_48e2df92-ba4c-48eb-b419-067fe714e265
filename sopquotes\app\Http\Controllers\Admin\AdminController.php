<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Quote;
use App\Models\User;
use App\Models\Category;
use App\Models\UserDownload;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function dashboard(): Response
    {
        // Authorization is handled by admin middleware

        // Get statistics
        $stats = [
            'totalQuotes' => Quote::count(),
            'totalUsers' => User::count(),
            'featuredQuotes' => Quote::where('is_featured', true)->count(),
            'totalDownloads' => UserDownload::count(),
            'newQuotesThisMonth' => Quote::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'newUsersThisMonth' => User::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        // Get recent quotes
        $recentQuotes = Quote::with(['category', 'creator'])
            ->latest()
            ->take(5)
            ->get();

        // Get recent users
        $recentUsers = User::latest()
            ->take(5)
            ->get();

        return Inertia::render('admin/dashboard', [
            'stats' => $stats,
            'recentQuotes' => $recentQuotes,
            'recentUsers' => $recentUsers,
        ]);
    }
}
