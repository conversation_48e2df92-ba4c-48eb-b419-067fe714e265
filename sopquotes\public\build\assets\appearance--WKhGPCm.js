import{u as p,j as e,$ as d,L as m}from"./app-BWHLaRS2.js";import{c as n,B as u}from"./button-CUovRkQ3.js";import{S as x,M as h,a as g}from"./sun-Bmefm2yw.js";import{A as f}from"./app-layout-BUzV5jF3.js";import{S as j}from"./separator-2rtRMRGV.js";import"./book-open-DxiZ0b6m.js";import"./index-BDjL_iy4.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./flash-message-BOB8jroJ.js";import"./settings-FGMHgWsK.js";import"./calendar-D56_txMz.js";import"./chevron-right-fzPpalsz.js";import"./user-CmroWLXK.js";function b({className:s="",...a}){const{appearance:t,updateAppearance:l}=p(),i=[{value:"light",icon:x,label:"Light"},{value:"dark",icon:h,label:"Dark"},{value:"system",icon:g,label:"System"}];return e.jsx("div",{className:n("inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800",s),...a,children:i.map(({value:r,icon:c,label:o})=>e.jsxs("button",{onClick:()=>l(r),className:n("flex items-center rounded-md px-3.5 py-1.5 transition-colors",t===r?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"),children:[e.jsx(c,{className:"-ml-1 h-4 w-4"}),e.jsx("span",{className:"ml-1.5 text-sm",children:o})]},r))})}function N({title:s,description:a}){return e.jsxs("header",{children:[e.jsx("h3",{className:"mb-0.5 text-base font-medium",children:s}),a&&e.jsx("p",{className:"text-muted-foreground text-sm",children:a})]})}function y({title:s,description:a}){return e.jsxs("div",{className:"mb-8 space-y-0.5",children:[e.jsx("h2",{className:"text-xl font-semibold tracking-tight",children:s}),a&&e.jsx("p",{className:"text-muted-foreground text-sm",children:a})]})}const w=[{title:"Profile",url:"/settings/profile",icon:null},{title:"Password",url:"/settings/password",icon:null},{title:"Appearance",url:"/settings/appearance",icon:null}];function v({children:s}){if(typeof window>"u")return null;const a=window.location.pathname;return e.jsxs("div",{className:"px-4 py-6",children:[e.jsx(y,{title:"Settings",description:"Manage your profile and account settings"}),e.jsxs("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-12",children:[e.jsx("aside",{className:"w-full max-w-xl lg:w-48",children:e.jsx("nav",{className:"flex flex-col space-y-1 space-x-0",children:w.map(t=>e.jsx(u,{size:"sm",variant:"ghost",asChild:!0,className:n("w-full justify-start",{"bg-muted":a===t.url}),children:e.jsx(d,{href:t.url,prefetch:!0,children:t.title})},t.url))})}),e.jsx(j,{className:"my-6 md:hidden"}),e.jsx("div",{className:"flex-1 md:max-w-2xl",children:e.jsx("section",{className:"max-w-xl space-y-12",children:s})})]})]})}const k=[{title:"Appearance settings",href:"/settings/appearance"}];function R(){return e.jsxs(f,{breadcrumbs:k,children:[e.jsx(m,{title:"Appearance settings"}),e.jsx(v,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(N,{title:"Appearance settings",description:"Update your account's appearance settings"}),e.jsx(b,{})]})})]})}export{R as default};
