<?php

namespace App\Policies;

use App\Models\Quote;
use App\Models\User;

class QuotePolicy
{
    /**
     * Determine whether the user can view any quotes.
     */
    public function viewAny(?User $user): bool
    {
        return true; // Anyone can view quotes
    }

    /**
     * Determine whether the user can view the quote.
     */
    public function view(?User $user, Quote $quote): bool
    {
        return $quote->is_active; // Anyone can view active quotes
    }

    /**
     * Determine whether the user can create quotes.
     */
    public function create(?User $user): bool
    {
        return $user && $user->isAdmin(); // Only admins can create quotes
    }

    /**
     * Determine whether the user can update the quote.
     */
    public function update(?User $user, Quote $quote): bool
    {
        return $user && $user->isAdmin(); // Only admins can update quotes
    }

    /**
     * Determine whether the user can delete the quote.
     */
    public function delete(?User $user, Quote $quote): bool
    {
        return $user && $user->isAdmin(); // Only admins can delete quotes
    }
}
