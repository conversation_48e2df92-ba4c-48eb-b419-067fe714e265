import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Search, Users, Shield, ShieldCheck, Edit, Trash2, User<PERSON><PERSON><PERSON>, UserX, Plus } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';


export default function AdminUsersIndex({ users = { data: [], total: 0, links: [] }, filters = {}, auth, adminCount = 1 }) {
    // Ensure filters is an object, not an array
    const safeFilters = Array.isArray(filters) ? {} : filters;

    const [searchTerm, setSearchTerm] = useState(String(safeFilters.search || ''));
    const [verifiedFilter, setVerifiedFilter] = useState(String(safeFilters.verified || ''));





    const handleSearch = (e) => {
        e.preventDefault();

        // Build query parameters, excluding empty values
        const params = {};
        if (searchTerm.trim()) params.search = searchTerm.trim();
        if (verifiedFilter) params.verified = verifiedFilter;

        router.get('/admin/users', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // Handle filter changes with immediate effect
    const handleFilterChange = (filterType, value) => {
        const params = {};

        // Set current values
        if (searchTerm.trim()) params.search = searchTerm.trim();
        if (filterType === 'verified') {
            setVerifiedFilter(value);
            if (value) params.verified = value;
        }

        router.get('/admin/users', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // Clear all filters
    const handleClearFilters = () => {
        setSearchTerm('');
        setVerifiedFilter('');
        router.get('/admin/users', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };



    const handleToggleStatus = (user) => {
        const action = user.is_active ? 'deactivate' : 'activate';
        if (confirm(`Are you sure you want to ${action} ${user.name}?`)) {
            router.patch(`/admin/users/${user.id}/toggle-status`);
        }
    };

    const handleDelete = (user) => {
        if (confirm(`Are you sure you want to delete ${user.name}? This action cannot be undone.`)) {
            router.delete(`/admin/users/${user.id}`);
        }
    };

    return (
        <AdminLayout>
            <Head title="Manage Users - Admin Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>

                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            View and manage all admin users in the system
                        </p>
                    </div>
                    <Link href="/admin/users/create">
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Admin
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Input
                                        type="text"
                                        placeholder="Search users..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>



                                <div>
                                    <select
                                        value={verifiedFilter}
                                        onChange={(e) => handleFilterChange('verified', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Status</option>
                                        <option value="verified">Verified</option>
                                        <option value="unverified">Unverified</option>
                                    </select>
                                </div>

                                <div className="flex space-x-2">
                                    <Button type="submit">
                                        <Search className="h-4 w-4 mr-2" />
                                        Search
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleClearFilters}
                                    >
                                        Clear Filters
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Users List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Users ({users?.total || 0})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {users?.data?.length > 0 ? users.data.map((user) => (
                                <div key={user.id} className="border rounded-lg p-6">
                                    <div className="flex justify-between items-start">
                                        <div className="flex items-start space-x-4">
                                            <div className="h-12 w-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                <span className="text-lg font-medium text-gray-700 dark:text-gray-200">
                                                    {user.name?.charAt(0) || 'U'}
                                                </span>
                                            </div>

                                            <div className="flex-1">
                                                <div className="flex items-center space-x-2 mb-2">
                                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                        {user.name}
                                                    </h3>
                                                    {user.role === 'admin' && (
                                                        <Badge className="bg-purple-100 text-purple-800">
                                                            <Shield className="h-3 w-3 mr-1" />
                                                            Admin
                                                        </Badge>
                                                    )}
                                                    {user.email_verified_at ? (
                                                        <Badge className="bg-green-100 text-green-800">
                                                            <ShieldCheck className="h-3 w-3 mr-1" />
                                                            Verified
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="outline" className="text-yellow-600">
                                                            Unverified
                                                        </Badge>
                                                    )}
                                                    {user.is_active === false && (
                                                        <Badge variant="destructive">
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </div>

                                                <p className="text-gray-600 dark:text-gray-400 mb-1">
                                                    {user.email}
                                                </p>

                                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                                    <p>Joined: {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}</p>
                                                    <p>Last active: {user.updated_at ? new Date(user.updated_at).toLocaleDateString() : 'Unknown'}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex flex-col space-y-2 ml-4">
                                            {/* Only show deactivate/delete if more than 1 admin exists or this isn't an admin */}
                                            {(adminCount > 1 || user.role !== 'admin') && (
                                                <Button
                                                    variant={user.is_active ? "outline" : "default"}
                                                    size="sm"
                                                    onClick={() => handleToggleStatus(user)}
                                                    className="w-full"
                                                >
                                                    {user.is_active ? (
                                                        <>
                                                            <UserX className="h-4 w-4 mr-2" />
                                                            Deactivate
                                                        </>
                                                    ) : (
                                                        <>
                                                            <UserCheck className="h-4 w-4 mr-2" />
                                                            Activate
                                                        </>
                                                    )}
                                                </Button>
                                            )}

                                            {user.id !== auth.user.id && (adminCount > 1 || user.role !== 'admin') && (
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => handleDelete(user)}
                                                    className="w-full"
                                                >
                                                    <Trash2 className="h-4 w-4 mr-2" />
                                                    Delete
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">No users found</p>
                                </div>
                            )}

                            {/* Remove duplicate empty state */}
                            {false && (
                                <div className="text-center py-12">
                                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-500 text-lg">No users found</p>
                                </div>
                            )}
                        </div>

                        {/* Pagination */}
                        {users.links && (
                            <div className="flex justify-center mt-6">
                                <div className="flex space-x-2">
                                    {users.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            className={`px-3 py-2 text-sm rounded-md ${link.active
                                                ? 'bg-blue-600 text-white'
                                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
