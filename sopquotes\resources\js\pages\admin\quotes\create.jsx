import { Head, Link, useForm } from '@inertiajs/react';
import { useState } from 'react';
import { ArrowLeft, Save, Upload, FileText, Volume2, Plus, X } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { Checkbox } from '@/components/ui/checkbox';

export default function AdminQuoteCreate({ categories, auth }) {
    const [tags, setTags] = useState([]);
    const [newTag, setNewTag] = useState('');



    const { data, setData, post, processing, errors, reset } = useForm({
        text: '',
        source: '',
        author: '',
        reference: '',
        category_id: '',
        tags: [],
        is_featured: false,
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        post(route('admin.quotes.store'), {
            data: {
                ...data,
                tags: JSON.stringify(tags),
            }
        });
    };

    const addTag = () => {
        if (newTag.trim() && !tags.includes(newTag.trim())) {
            const updatedTags = [...tags, newTag.trim()];
            setTags(updatedTags);
            setData('tags', updatedTags);
            setNewTag('');
        }
    };

    const removeTag = (tagToRemove) => {
        const updatedTags = tags.filter(tag => tag !== tagToRemove);
        setTags(updatedTags);
        setData('tags', updatedTags);
    };



    return (
        <AdminLayout>
            <Head title="Create Quote - Admin Dashboard" />

            <div className="max-w-4xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>

                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Add a new inspirational quote to the SOP Quotes collection
                        </p>
                    </div>
                    <Link href="/admin/quotes">
                        <Button variant="outline">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Quotes
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardContent className="p-6">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Quote Text */}
                            <div className="space-y-2">
                                <Label htmlFor="text">Quote Text *</Label>
                                <Textarea
                                    id="text"
                                    value={data.text}
                                    onChange={(e) => setData('text', e.target.value)}
                                    placeholder="Enter the inspirational quote..."
                                    rows={4}
                                    className={errors.text ? 'border-red-500' : ''}
                                />
                                {errors.text && <p className="text-red-500 text-sm">{errors.text}</p>}
                            </div>

                            {/* Reference */}
                            <div className="space-y-2">
                                <Label htmlFor="reference">Reference</Label>
                                <Input
                                    id="reference"
                                    value={data.reference}
                                    onChange={(e) => setData('reference', e.target.value)}
                                    placeholder="e.g., Steps to Christ, p. 123"
                                    className={errors.reference ? 'border-red-500' : ''}
                                />
                                {errors.reference && <p className="text-red-500 text-sm">{errors.reference}</p>}
                            </div>

                            {/* Author */}
                            <div className="space-y-2">
                                <Label htmlFor="author">Author</Label>
                                <Input
                                    id="author"
                                    value={data.author}
                                    onChange={(e) => setData('author', e.target.value)}
                                    placeholder="e.g., Ellen G. White"
                                    className={errors.author ? 'border-red-500' : ''}
                                />
                                {errors.author && <p className="text-red-500 text-sm">{errors.author}</p>}
                            </div>

                            {/* Source */}
                            <div className="space-y-2">
                                <Label htmlFor="source">Source</Label>
                                <Input
                                    id="source"
                                    value={data.source}
                                    onChange={(e) => setData('source', e.target.value)}
                                    placeholder="e.g., Steps to Christ"
                                    className={errors.source ? 'border-red-500' : ''}
                                />
                                {errors.source && <p className="text-red-500 text-sm">{errors.source}</p>}
                            </div>

                            {/* Category */}
                            <div className="space-y-2">
                                <Label htmlFor="category">Category</Label>
                                <select
                                    id="category"
                                    value={data.category_id}
                                    onChange={(e) => setData('category_id', e.target.value)}
                                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.category_id ? 'border-red-500' : 'border-gray-300'}`}
                                >
                                    <option value="">Select a category</option>
                                    {categories.map((category) => (
                                        <option key={category.id} value={category.id.toString()}>
                                            {category.name}
                                        </option>
                                    ))}
                                </select>
                                {errors.category_id && <p className="text-red-500 text-sm">{errors.category_id}</p>}
                            </div>

                            {/* Tags */}
                            <div className="space-y-2">
                                <Label htmlFor="tags">Tags</Label>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Add tags to help categorize and organize this quote for easier searching and filtering.
                                </p>
                                <div className="flex gap-2 mb-2">
                                    <Input
                                        value={newTag}
                                        onChange={(e) => setNewTag(e.target.value)}
                                        placeholder="Add a tag..."
                                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                                    />
                                    <Button type="button" onClick={addTag} variant="outline" size="sm">
                                        <Plus className="h-4 w-4" />
                                    </Button>
                                </div>
                                <div className="flex flex-wrap gap-2">
                                    {tags.map((tag, index) => (
                                        <span key={index} className="inline-flex items-center gap-1 rounded-full bg-gray-200 px-2 py-1 text-xs text-gray-800 dark:bg-gray-700 dark:text-gray-100">
                                            {tag}
                                            <button
                                                type="button"
                                                onClick={() => removeTag(tag)}
                                                className="ml-1 hover:text-red-500 transition-colors"
                                            >
                                                <X className="h-3 w-3" />
                                            </button>
                                        </span>
                                    ))}
                                </div>
                                {errors.tags && <p className="text-red-500 text-sm">{errors.tags}</p>}
                            </div>



                            {/* Featured Checkbox */}
                            <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="featured"
                                        checked={data.is_featured}
                                        onCheckedChange={(checked) => setData('is_featured', checked)}
                                    />
                                    <Label htmlFor="featured">Mark as featured quote</Label>
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Featured quotes are highlighted prominently on the main quotes page and appear in the "Featured Quotes" section.
                                </p>
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Button type="button" variant="outline" onClick={() => reset()}>
                                    Reset
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? (
                                        <>
                                            <Upload className="h-4 w-4 mr-2 animate-spin" />
                                            Creating...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4 mr-2" />
                                            Create Quote
                                        </>
                                    )}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
