import { Head, <PERSON>, usePage } from '@inertiajs/react';
import { Heart, BookOpen, Users } from 'lucide-react';
import MenorahIcon from '@/components/menorah-icon';

export default function Welcome() {
    const { auth } = usePage().props;

    return (
        <>
            <Head title="Welcome to SOP Quotes">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
                <header className="w-full px-6 py-4 bg-white">
                    <nav className="flex items-center justify-between max-w-7xl mx-auto">
                        <div className="flex items-center space-x-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600">
                                <BookOpen className="h-6 w-6 text-white" />
                            </div>
                            <span className="text-2xl font-bold text-gray-900 dark:text-white">Lapota SOP</span>
                        </div>
                        <div className="flex items-center gap-4 hidden sm:block">
                            <Link
                                href="/quotes"
                                className="inline-block rounded-lg px-6 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                            >
                                Browse Quotes
                            </Link>
                            <Link
                                href="/about"
                                className="inline-block rounded-lg px-6 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                            >
                                About
                            </Link>
                        </div>
                    </nav>
                </header>
                <main className="flex-1 flex items-center justify-center px-6 py-12">
                    <div className="max-w-6xl mx-auto text-center">
                        {/* Hero Section */}
                        <div className="mb-16">
                            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6">
                                Find <span className="text-blue-600">Inspiration</span> in God's Word
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                                Discover daily inspiration through Christian quotes, Bible verses, and spiritual resources
                                to strengthen your faith journey.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link
                                    href="/quotes/daily"
                                    className="inline-flex items-center justify-center rounded-lg bg-blue-600 px-8 py-4 text-lg font-medium text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                                >
                                    <MenorahIcon className="h-5 w-5 mr-2 bg-white rounded-[5px]" />
                                    Daily Quote
                                </Link>
                                <Link
                                    href="/quotes"
                                    className="inline-flex items-center justify-center rounded-lg border border-blue-200 bg-white px-8 py-4 text-lg font-medium text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700"
                                >
                                    <BookOpen className="h-5 w-5 mr-2" />
                                    Browse Quotes
                                </Link>
                                <Link
                                    href="/about"
                                    className="inline-flex items-center justify-center rounded-lg border border-green-200 bg-white px-8 py-4 text-lg font-medium text-green-600 hover:bg-green-50 dark:border-green-800 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700"
                                >
                                    <Heart className="h-5 w-5 mr-2" />
                                    About Infor
                                </Link>
                            </div>
                        </div>

                        {/* Features Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    Inspiring Quotes
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Discover thousands of uplifting quotes from the Bible and renowned Christian authors
                                    to encourage your daily walk with God.
                                </p>
                            </div>

                            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                <div className="bg-green-100 dark:bg-green-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    Personal Favorites
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Save your favorite quotes and verses to create your own personal collection
                                    of spiritual encouragement.
                                </p>
                            </div>

                            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                <div className="bg-purple-100 dark:bg-purple-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Users className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                    Community Driven
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Join a community of believers sharing wisdom and encouragement through
                                    God's timeless truths.
                                </p>
                            </div>
                        </div>

                        {/* Call to Action */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Start Your Spiritual Journey Today
                            </h2>
                            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                                Find daily encouragement through God's Word and join our community of believers.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link
                                    href="/quotes"
                                    className="inline-flex items-center justify-center rounded-lg bg-blue-600 px-8 py-4 text-lg font-medium text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                                >
                                    <BookOpen className="h-5 w-5 mr-2" />
                                    Explore Quotes
                                </Link>
                            </div>
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-6 py-8">
                        <div className="flex flex-col md:flex-row justify-between items-center">
                            <div className="flex items-center space-x-3 mb-4 md:mb-0">
                                <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600">
                                    <BookOpen className="h-4 w-4 text-white" />
                                </div>
                                <span className="text-lg font-semibold text-gray-900 dark:text-white">SOP Quotes</span>
                            </div>
                            <div className="flex space-x-6">
                                <Link href="/quotes" className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400">
                                    Quotes
                                </Link>
                                <Link href="/quotes/daily" className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400">
                                    Daily Quote
                                </Link>
                                <Link href="/about" className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400">
                                    About
                                </Link>
                            </div>
                        </div>
                        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 text-center">
                            <p className="text-gray-500 dark:text-gray-400">
                                © 2024 LK Soft Development. Spreading God's love through His Word.
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
