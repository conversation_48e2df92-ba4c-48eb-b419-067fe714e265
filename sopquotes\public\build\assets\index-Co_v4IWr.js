import{r as d,j as e,L as $,$ as i,S as m}from"./app-BWHLaRS2.js";import{A as L}from"./admin-layout-DKXm94gZ.js";import{B as t}from"./button-CUovRkQ3.js";import{C as u,a as g,b as f,c as v}from"./card-3ac6awgC.js";import{B as n}from"./badge-BSmw6aR2.js";import{I as D}from"./input-B4rX1XfI.js";import{P as y}from"./plus-CzNwqLHA.js";import{S as T}from"./search-H5umLGXz.js";import{c as F}from"./book-open-DxiZ0b6m.js";import{F as I,V as Q}from"./volume-2-DIbtoXia.js";import{E as z}from"./eye-CbhFpv9_.js";import{S as E}from"./square-pen-BLfc3BBH.js";import{T as B}from"./trash-2-DpyI9JiF.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./sun-Bmefm2yw.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],N=F("Star",M);function le({quotes:a={data:[],total:0,links:[]},categories:c=[],filters:x={}}){var p;const r=Array.isArray(x)?{}:x,[o,w]=d.useState((r==null?void 0:r.search)||""),[h,b]=d.useState((r==null?void 0:r.category)||""),[j,S]=d.useState((r==null?void 0:r.status)||""),C=s=>{s.preventDefault(),m.get("/admin/quotes",{search:o,category:h,status:j},{preserveState:!0,preserveScroll:!0})},A=s=>{var l;confirm(`Are you sure you want to delete the quote "${((l=s.text)==null?void 0:l.substring(0,50))||"this quote"}..."?`)&&m.delete(`/admin/quotes/${s.id}`)},k=s=>{m.patch(`/admin/quotes/${s.id}/toggle-featured`,{},{preserveState:!0,preserveScroll:!0})};return e.jsxs(L,{children:[e.jsx($,{title:"Manage Quotes - Admin Dashboard"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Manage Quotes"}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:["Create, edit, and manage all quotes in the system (",(a==null?void 0:a.total)||0," total)"]})]}),e.jsx(i,{href:"/admin/quotes/create",children:e.jsxs(t,{children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Create Quote"]})})]}),e.jsxs(u,{children:[e.jsx(g,{children:e.jsx(f,{children:"Filters"})}),e.jsx(v,{children:e.jsx("form",{onSubmit:C,className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{children:e.jsx(D,{type:"text",placeholder:"Search quotes...",value:o,onChange:s=>w(s.target.value)})}),e.jsx("div",{children:e.jsxs("select",{value:h,onChange:s=>b(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Categories"}),c==null?void 0:c.map(s=>e.jsx("option",{value:s.slug,children:s.name},s.id))]})}),e.jsx("div",{children:e.jsxs("select",{value:j,onChange:s=>S(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"featured",children:"Featured"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]})}),e.jsxs(t,{type:"submit",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Search"]})]})})})]}),e.jsxs(u,{children:[e.jsx(g,{children:e.jsxs(f,{children:["Quotes (",(a==null?void 0:a.total)||0,")"]})}),e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-4",children:[((p=a==null?void 0:a.data)==null?void 0:p.length)>0?a.data.map(s=>e.jsx("div",{className:"border rounded-lg p-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsxs("span",{className:"text-sm text-gray-500",children:["#",s.id]}),s.is_featured&&e.jsxs(n,{className:"bg-yellow-100 text-yellow-800",children:[e.jsx(N,{className:"h-3 w-3 mr-1"}),"Featured"]}),s.category&&e.jsx(n,{variant:"outline",children:s.category.name}),s.pdf_path&&e.jsxs(n,{variant:"secondary",children:[e.jsx(I,{className:"h-3 w-3 mr-1"}),"PDF"]}),s.audio_path&&e.jsxs(n,{variant:"secondary",children:[e.jsx(Q,{className:"h-3 w-3 mr-1"}),"Audio"]})]}),e.jsxs("blockquote",{className:"text-lg italic text-gray-800 dark:text-gray-200 mb-3",children:['"',s.text,'"']}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[s.reference&&e.jsxs("p",{children:[e.jsx("strong",{children:"Reference:"})," ",s.reference]}),s.author&&e.jsxs("p",{children:[e.jsx("strong",{children:"Author:"})," ",s.author]}),s.source&&e.jsxs("p",{children:[e.jsx("strong",{children:"Source:"})," ",s.source]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Created:"})," ",s.created_at?new Date(s.created_at).toLocaleDateString():"Unknown"]}),s.creator&&e.jsxs("p",{children:[e.jsx("strong",{children:"Created by:"})," ",s.creator.name]})]}),s.tags&&Array.isArray(s.tags)&&s.tags.length>0&&e.jsx("div",{className:"flex flex-wrap gap-1 mt-3",children:s.tags.map((l,_)=>e.jsx(n,{variant:"outline",className:"text-xs",children:l},_))})]}),e.jsxs("div",{className:"flex flex-col space-y-2 ml-4",children:[e.jsx(i,{href:`/quotes/${s.id}`,children:e.jsxs(t,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),"View"]})}),e.jsx(i,{href:`/admin/quotes/${s.id}/edit`,children:e.jsxs(t,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),"Edit"]})}),e.jsxs(t,{variant:s.is_featured?"default":"outline",size:"sm",onClick:()=>k(s),className:"w-full",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),s.is_featured?"Unfeature":"Feature"]}),e.jsxs(t,{variant:"destructive",size:"sm",onClick:()=>A(s),className:"w-full",children:[e.jsx(B,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})},s.id)):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("p",{className:"text-gray-500 text-lg",children:"No quotes found"}),e.jsx(i,{href:"/admin/quotes/create",className:"mt-4 inline-block",children:e.jsxs(t,{children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Create Your First Quote"]})})]}),!1]}),(a==null?void 0:a.links)&&a.links.length>3&&e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("div",{className:"flex space-x-2",children:a.links.map((s,l)=>e.jsx(i,{href:s.url||"#",className:`px-3 py-2 text-sm rounded-md ${s.active?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"} ${s.url?"":"opacity-50 cursor-not-allowed"}`,dangerouslySetInnerHTML:{__html:s.label}},l))})})]})]})]})]})}export{le as default};
