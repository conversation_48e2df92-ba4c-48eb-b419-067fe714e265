import{r as n,j as c}from"./app-BWHLaRS2.js";import{u as M,c as D}from"./button-CUovRkQ3.js";import{u as z,c as L,a as R,P as O,b as A}from"./index-BDjL_iy4.js";import{P as j}from"./index-C3-VNe4Y.js";import{c as B}from"./book-open-DxiZ0b6m.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],K=B("Check",H);function q(e){const t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var E="Checkbox",[T,Z]=L(E),[X,F]=T(E),w=n.forwardRef((e,t)=>{const{__scopeCheckbox:r,name:u,checked:p,defaultChecked:s,required:h,disabled:d,value:k="on",onCheckedChange:x,form:l,...C}=e,[a,m]=n.useState(null),v=M(t,o=>m(o)),y=n.useRef(!1),P=a?l||!!a.closest("form"):!0,[f=!1,g]=z({prop:p,defaultProp:s,onChange:x}),_=n.useRef(f);return n.useEffect(()=>{const o=a==null?void 0:a.form;if(o){const b=()=>g(_.current);return o.addEventListener("reset",b),()=>o.removeEventListener("reset",b)}},[a,g]),c.jsxs(X,{scope:r,state:f,disabled:d,children:[c.jsx(j.button,{type:"button",role:"checkbox","aria-checked":i(f)?"mixed":f,"aria-required":h,"data-state":N(f),"data-disabled":d?"":void 0,disabled:d,value:k,...C,ref:v,onKeyDown:R(e.onKeyDown,o=>{o.key==="Enter"&&o.preventDefault()}),onClick:R(e.onClick,o=>{g(b=>i(b)?!0:!b),P&&(y.current=o.isPropagationStopped(),y.current||o.stopPropagation())})}),P&&c.jsx($,{control:a,bubbles:!y.current,name:u,value:k,checked:f,required:h,disabled:d,form:l,style:{transform:"translateX(-100%)"},defaultChecked:i(s)?!1:s})]})});w.displayName=E;var I="CheckboxIndicator",S=n.forwardRef((e,t)=>{const{__scopeCheckbox:r,forceMount:u,...p}=e,s=F(I,r);return c.jsx(O,{present:u||i(s.state)||s.state===!0,children:c.jsx(j.span,{"data-state":N(s.state),"data-disabled":s.disabled?"":void 0,...p,ref:t,style:{pointerEvents:"none",...e.style}})})});S.displayName=I;var $=e=>{const{control:t,checked:r,bubbles:u=!0,defaultChecked:p,...s}=e,h=n.useRef(null),d=q(r),k=A(t);n.useEffect(()=>{const l=h.current,C=window.HTMLInputElement.prototype,m=Object.getOwnPropertyDescriptor(C,"checked").set;if(d!==r&&m){const v=new Event("click",{bubbles:u});l.indeterminate=i(r),m.call(l,i(r)?!1:r),l.dispatchEvent(v)}},[d,r,u]);const x=n.useRef(i(r)?!1:r);return c.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:p??x.current,...s,tabIndex:-1,ref:h,style:{...e.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function i(e){return e==="indeterminate"}function N(e){return i(e)?"indeterminate":e?"checked":"unchecked"}var G=w,J=S;function ee({className:e,...t}){return c.jsx(G,{"data-slot":"checkbox",className:D("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:c.jsx(J,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:c.jsx(K,{className:"size-3.5"})})})}export{ee as C};
