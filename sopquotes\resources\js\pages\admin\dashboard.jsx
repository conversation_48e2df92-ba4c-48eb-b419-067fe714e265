import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { BookO<PERSON>, Users, FileText, TrendingUp, Plus, Edit, Trash2, Eye, Download } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function AdminDashboard({ stats, recentQuotes, recentUsers, auth }) {
    return (
        <AdminLayout>
            <Head title="Admin Dashboard - SOP Quotes" />

            <div className="space-y-8">
                {/* Header */}
                <div>

                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                        Manage your SOP Quotes content and users
                    </p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.totalQuotes || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                +{stats?.newQuotesThisMonth || 0} this month
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                +{stats?.newUsersThisMonth || 0} this month
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Featured Quotes</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.featuredQuotes || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                Active featured content
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
                            <Download className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.totalDownloads || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                PDF & Audio downloads
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Link href="/admin/quotes/create">
                                <Button className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                                    <Plus className="h-6 w-6" />
                                    <span>Create New Quote</span>
                                </Button>
                            </Link>

                            <Link href="/admin/quotes">
                                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                                    <BookOpen className="h-6 w-6" />
                                    <span>Manage Quotes</span>
                                </Button>
                            </Link>

                            <Link href="/admin/users">
                                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                                    <Users className="h-6 w-6" />
                                    <span>Manage Users</span>
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Content */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Recent Quotes */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <CardTitle>Recent Quotes</CardTitle>
                            <Link href="/admin/quotes">
                                <Button variant="outline" size="sm">View All</Button>
                            </Link>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentQuotes?.map((quote) => (
                                    <div key={quote.id} className="flex items-start justify-between p-4 border rounded-lg">
                                        <div className="flex-1">
                                            <p className="text-sm font-medium line-clamp-2">
                                                "{quote.text}"
                                            </p>
                                            <div className="flex items-center space-x-2 mt-2">
                                                {quote.is_featured && (
                                                    <Badge variant="secondary">Featured</Badge>
                                                )}
                                                {quote.category && (
                                                    <Badge variant="outline">{quote.category.name}</Badge>
                                                )}
                                            </div>
                                            <p className="text-xs text-gray-500 mt-1">
                                                Created {new Date(quote.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="flex space-x-1 ml-4">
                                            <Link href={`/quotes/${quote.id}`}>
                                                <Button variant="ghost" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            <Link href={`/admin/quotes/${quote.id}/edit`}>
                                                <Button variant="ghost" size="sm">
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}

                                {(!recentQuotes || recentQuotes.length === 0) && (
                                    <p className="text-gray-500 text-center py-4">No quotes yet</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Users */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <CardTitle>Recent Users</CardTitle>
                            <Link href="/admin/users">
                                <Button variant="outline" size="sm">View All</Button>
                            </Link>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentUsers?.map((user) => (
                                    <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                                                    {user.name?.charAt(0) || 'U'}
                                                </span>
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium">{user.name}</p>
                                                <p className="text-xs text-gray-500">{user.email}</p>
                                                <p className="text-xs text-gray-500">
                                                    Joined {new Date(user.created_at).toLocaleDateString()}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {user.role === 'admin' && (
                                                <Badge>Admin</Badge>
                                            )}
                                            <Badge variant="outline">
                                                {user.email_verified_at ? 'Verified' : 'Unverified'}
                                            </Badge>
                                        </div>
                                    </div>
                                ))}

                                {(!recentUsers || recentUsers.length === 0) && (
                                    <p className="text-gray-500 text-center py-4">No users yet</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AdminLayout>
    );
}
