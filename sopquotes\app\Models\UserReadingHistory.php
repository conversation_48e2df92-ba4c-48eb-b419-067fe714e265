<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class UserReadingHistory extends Model
{
    use HasFactory;

    protected $table = 'user_reading_history';

    protected $fillable = [
        'user_id',
        'readable_type',
        'readable_id',
        'viewed_at',
        'metadata',
    ];

    protected $casts = [
        'viewed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the reading history.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the readable model (Quote, Book, Media).
     */
    public function readable(): MorphTo
    {
        return $this->morphTo();
    }
}
