import { Head, <PERSON> } from '@inertiajs/react';
import { Share2, BookOpen } from 'lucide-react';
import MenorahIcon from '@/components/menorah-icon';
import QuoteImageGenerator from '@/components/quote-image-generator';
import QuotesLayout from '@/layouts/quotes-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function QuotesDaily({ quote, auth }) {
    const shareQuote = async () => {
        if (!quote) return;

        const shareData = {
            title: 'Daily Christian Quote',
            text: `"${quote.text}" ${quote.reference ? `- ${quote.reference}` : ''}`,
            url: window.location.href,
        };

        try {
            if (navigator.share) {
                await navigator.share(shareData);
            } else {
                await navigator.clipboard.writeText(`${shareData.text}\n\n${shareData.url}`);
                alert('Quote copied to clipboard!');
            }
        } catch (error) {
            console.error('Failed to share:', error);
            alert('Unable to share quote. Please try again.');
        }
    };



    if (!quote) {
        return (
            <QuotesLayout>
                <Head title="Daily Quote - SOP Quotes" />
                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    <div className="text-center py-12">
                        <MenorahIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            No daily quote available
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                            Please check back later for today's inspirational quote.
                        </p>
                        <Link href="/quotes">
                            <Button variant="outline">
                                <BookOpen className="h-4 w-4 mr-2" />
                                Browse All Quotes
                            </Button>
                        </Link>
                    </div>
                </div>
            </QuotesLayout>
        );
    }

    return (
        <QuotesLayout>
            <Head title="Daily Quote - SOP Quotes" />

            <div className="container mx-auto px-4 py-8 max-w-4xl">
                {/* Header */}
                <div className="text-center mb-8">
                    <div className="flex items-center justify-center mb-4">
                        <MenorahIcon className="h-8 w-8 text-orange-500 mr-3" />
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Daily Inspiration
                        </h1>
                    </div>
                    <p className="text-lg text-gray-600 dark:text-gray-400">
                        Start your day with wisdom from the Spirit of Prophecy
                    </p>
                </div>

                {/* Quote Card */}
                <Card className="mb-8 shadow-lg">
                    <CardHeader className="text-center pb-8 bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20">
                        <div className="flex justify-center mb-4">
                            <MenorahIcon className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                        </div>
                        <blockquote className="text-2xl md:text-3xl italic text-gray-800 dark:text-gray-200 leading-relaxed font-light max-w-3xl mx-auto">
                            "{quote.text}"
                        </blockquote>
                    </CardHeader>

                    <CardContent className="space-y-6 p-8">
                        {/* Attribution */}
                        <div className="text-center space-y-3">
                            {quote.reference && (
                                <p className="text-xl font-bold text-blue-700 dark:text-blue-300">
                                    {quote.reference}
                                </p>
                            )}
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap justify-center gap-3 pt-4">
                            {quote.id && (
                                <Link href={`/quotes/${quote.id}`}>
                                    <Button variant="outline" className="min-w-[120px]">
                                        <BookOpen className="h-4 w-4 mr-2" />
                                        View Details
                                    </Button>
                                </Link>
                            )}
                        </div>

                        {/* Quote Info */}
                        <div className="text-center pt-4 border-t border-gray-100 dark:border-gray-700">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Daily quote for {new Date().toLocaleDateString('en-US', {
                                    weekday: 'long',
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                })}
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Navigation */}
                <div className="text-center">
                    <Link href="/quotes">
                        <Button variant="outline">
                            <BookOpen className="h-4 w-4 mr-2" />
                            Browse All Quotes
                        </Button>
                    </Link>
                </div>
            </div>
        </QuotesLayout>
    );
}