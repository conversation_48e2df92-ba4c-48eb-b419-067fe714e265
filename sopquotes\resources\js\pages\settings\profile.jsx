import { Head, useForm } from '@inertiajs/react';
import { User, Mail, Save, Trash2 } from 'lucide-react';
import SettingsLayout from '@/layouts/settings/layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useState } from 'react';

export default function Profile({ auth, mustVerifyEmail, status }) {
    const user = auth.user;
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

    const { data, setData, patch, errors, processing, recentlySuccessful } = useForm({
        name: user.name,
        email: user.email,
    });

    const { delete: destroy, processing: deleteProcessing } = useForm({
        password: '',
    });

    const submit = (e) => {
        e.preventDefault();
        patch(route('profile.update'));
    };

    const deleteAccount = (e) => {
        e.preventDefault();
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            destroy(route('profile.destroy'));
        }
    };

    return (
        <SettingsLayout>
            <Head title="Profile Settings" />

            <div className="space-y-6">
                {/* Profile Information */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <User className="h-5 w-5" />
                            <span>Profile Information</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-4">
                            <div>
                                <Label htmlFor="name">Name</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    required
                                    autoFocus
                                    autoComplete="name"
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-600 mt-1">{errors.name}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    required
                                    autoComplete="username"
                                />
                                {errors.email && (
                                    <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                                )}
                            </div>

                            {mustVerifyEmail && user.email_verified_at === null && (
                                <Alert>
                                    <Mail className="h-4 w-4" />
                                    <AlertDescription>
                                        Your email address is unverified. Please check your email for a verification link.
                                    </AlertDescription>
                                </Alert>
                            )}

                            <div className="flex items-center space-x-4">
                                <Button type="submit" disabled={processing}>
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Changes
                                </Button>

                                {recentlySuccessful && (
                                    <p className="text-sm text-green-600">Profile updated successfully.</p>
                                )}
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Delete Account */}
                <Card className="border-red-200 dark:border-red-800">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2 text-red-600 dark:text-red-400">
                            <Trash2 className="h-5 w-5" />
                            <span>Delete Account</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                            Once your account is deleted, all of its resources and data will be permanently deleted.
                            Before deleting your account, please download any data or information that you wish to retain.
                        </p>

                        <Button
                            variant="destructive"
                            onClick={deleteAccount}
                            disabled={deleteProcessing}
                        >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Account
                        </Button>
                    </CardContent>
                </Card>
            </div>
        </SettingsLayout>
    );
}
