<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Add foreign key to link quotes to media files
            $table->foreignId('media_id')->nullable()->after('category_id')->constrained('media')->onDelete('set null');

            // Remove old file path columns since we'll use the media relationship
            $table->dropColumn(['pdf_path', 'audio_path']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Restore old file path columns
            $table->string('pdf_path')->nullable()->after('tags');
            $table->string('audio_path')->nullable()->after('pdf_path');

            // Remove media relationship
            $table->dropForeign(['media_id']);
            $table->dropColumn('media_id');
        });
    }
};
