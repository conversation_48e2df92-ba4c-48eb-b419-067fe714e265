import{m as n,j as s,L as d}from"./app-BWHLaRS2.js";import{I as l}from"./input-error-Cz-64hVg.js";import{B as c}from"./button-CUovRkQ3.js";import{I as u}from"./input-B4rX1XfI.js";import{L as f}from"./label-DWihlnjb.js";import{A as w}from"./auth-layout-BCFIpUOr.js";import{L as h}from"./loader-circle-Bb-8g7No.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./card-3ac6awgC.js";import"./book-open-DxiZ0b6m.js";function F(){const{data:a,setData:e,post:t,processing:o,errors:i,reset:p}=n({password:""}),m=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>p("password")})};return s.jsxs(w,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(d,{title:"Confirm password"}),s.jsx("form",{onSubmit:m,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(f,{htmlFor:"password",children:"Password"}),s.jsx(u,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:a.password,autoFocus:!0,onChange:r=>e("password",r.target.value)}),s.jsx(l,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(c,{className:"w-full",disabled:o,children:[o&&s.jsx(h,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{F as default};
