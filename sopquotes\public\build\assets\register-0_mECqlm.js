import{m as x,j as e,L as u}from"./app-BWHLaRS2.js";import{I as i}from"./input-error-Cz-64hVg.js";import{T as n}from"./text-link-BJC5FN6k.js";import{B as g}from"./button-CUovRkQ3.js";import{I as d}from"./input-B4rX1XfI.js";import{L as l}from"./label-DWihlnjb.js";import{A as h}from"./auth-layout-BCFIpUOr.js";import{L as f}from"./loader-circle-Bb-8g7No.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./card-3ac6awgC.js";import"./book-open-DxiZ0b6m.js";function q(){const{data:s,setData:t,post:m,processing:r,errors:o,reset:c}=x({name:"",email:"",password:"",password_confirmation:""}),p=a=>{a.preventDefault(),m(route("register"),{onFinish:()=>c("password","password_confirmation")})};return e.jsxs(h,{title:"Create an account",description:"Enter your details below to create your account",children:[e.jsx(u,{title:"Register"}),e.jsxs("form",{className:"flex flex-col gap-6",onSubmit:p,children:[e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(l,{htmlFor:"name",children:"Name"}),e.jsx(d,{id:"name",type:"text",required:!0,autoFocus:!0,tabIndex:1,autoComplete:"name",value:s.name,onChange:a=>t("name",a.target.value),disabled:r,placeholder:"Full name"}),e.jsx(i,{message:o.name,className:"mt-2"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(l,{htmlFor:"email",children:"Email address"}),e.jsx(d,{id:"email",type:"email",required:!0,tabIndex:2,autoComplete:"email",value:s.email,onChange:a=>t("email",a.target.value),disabled:r,placeholder:"<EMAIL>"}),e.jsx(i,{message:o.email})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(l,{htmlFor:"password",children:"Password"}),e.jsx(d,{id:"password",type:"password",required:!0,tabIndex:3,autoComplete:"new-password",value:s.password,onChange:a=>t("password",a.target.value),disabled:r,placeholder:"Password"}),e.jsx(i,{message:o.password})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(l,{htmlFor:"password_confirmation",children:"Confirm password"}),e.jsx(d,{id:"password_confirmation",type:"password",required:!0,tabIndex:4,autoComplete:"new-password",value:s.password_confirmation,onChange:a=>t("password_confirmation",a.target.value),disabled:r,placeholder:"Confirm password"}),e.jsx(i,{message:o.password_confirmation})]}),e.jsxs(g,{type:"submit",className:"mt-6 w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5",tabIndex:5,disabled:r,children:[r&&e.jsx(f,{className:"h-4 w-4 animate-spin mr-2"}),r?"Creating account...":"Create Account"]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("span",{className:"w-full border-t border-gray-200 dark:border-gray-700"})}),e.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:e.jsx("span",{className:"bg-white dark:bg-gray-800 px-2 text-gray-500 dark:text-gray-400",children:"Already have an account?"})})]}),e.jsx("div",{className:"text-center",children:e.jsx(n,{href:route("login"),tabIndex:6,className:"text-blue-600 hover:text-blue-700 font-medium",children:"Sign in instead"})}),e.jsx("div",{className:"text-center pt-4 border-t border-gray-100 dark:border-gray-700",children:e.jsx(n,{href:route("home"),tabIndex:7,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 text-sm",children:"← Back to Home"})})]})]})}export{q as default};
