import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Search, Filter, Heart, BookOpen, Tag, User, Calendar, X, Plus, FileText, Volume2, Download } from 'lucide-react';
import QuotesLayout from '@/layouts/quotes-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

export default function QuotesIndex({ quotes, categories, filters = {}, auth }) {
    // Ensure filters is an object, not an array
    const safeFilters = Array.isArray(filters) ? {} : filters;

    const [searchTerm, setSearchTerm] = useState(String(safeFilters.search || ''));
    const [selectedCategory, setSelectedCategory] = useState(String(safeFilters.category || ''));
    const [favoriteLoading, setFavoriteLoading] = useState({});

    const handleSearch = (e) => {
        e.preventDefault();
        router.get('/quotes', {
            search: searchTerm,
            category: selectedCategory,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleCategoryFilter = (categorySlug) => {
        setSelectedCategory(categorySlug);
        router.get('/quotes', {
            search: searchTerm,
            category: categorySlug,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const toggleFavorite = async (quoteId) => {
        setFavoriteLoading(prev => ({ ...prev, [quoteId]: true }));

        try {
            const response = await fetch(`/favorites/quote/${quoteId}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            });

            const data = await response.json();

            if (data.success) {
                // Update the quote's favorite status in the local state
                router.reload({ only: ['quotes'] });
            } else {
                alert('Failed to update favorite status');
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            alert('Failed to update favorite status');
        } finally {
            setFavoriteLoading(prev => ({ ...prev, [quoteId]: false }));
        }
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedCategory('');
        router.get('/quotes', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    return (
        <QuotesLayout>
            <Head title="Browse Quotes - SOP Quotes" />

            <div className="container mx-auto px-4 py-8 max-w-7xl">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        Browse Inspirational Quotes
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                        Discover wisdom and inspiration from God's Word and Christian authors
                    </p>
                </div>

                {/* Search and Filters */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                    <form onSubmit={handleSearch} className="space-y-4">
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="flex-1">
                                <Input
                                    type="text"
                                    placeholder="Search quotes..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full"
                                />
                            </div>
                            <Button type="submit" className="md:w-auto">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </form>

                    {/* Category Filters */}
                    <div className="mt-6">
                        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Filter by Category:
                        </h3>
                        <div className="flex flex-wrap gap-2">
                            <Button
                                variant={selectedCategory === '' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => handleCategoryFilter('')}
                            >
                                All Categories
                            </Button>

                            {categories.map((category) => (
                                <Button
                                    key={category.id}
                                    variant={selectedCategory == category.slug ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleCategoryFilter(category.slug)}
                                    style={{
                                        backgroundColor: selectedCategory == category.slug ? category.color : 'transparent',
                                        borderColor: category.color,
                                        color: selectedCategory == category.slug ? 'white' : category.color,
                                    }}
                                >
                                    <Tag className="h-3 w-3 mr-1" />
                                    {category.name}
                                </Button>
                            ))}
                        </div>
                    </div>

                    {/* Additional Filters */}
                    <div className="mt-6">
                        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Additional Filters:
                        </h3>
                        <div className="flex flex-wrap gap-2">
                            <Button
                                variant={safeFilters.favorites ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => {
                                    router.get('/quotes', {
                                        ...safeFilters,
                                        favorites: safeFilters.favorites ? '' : '1',
                                    }, {
                                        preserveState: true,
                                        preserveScroll: true,
                                    });
                                }}
                            >
                                <Heart className="h-4 w-4 mr-2" />
                                My Favorites
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Featured Quotes */}
                {quotes.data && quotes.data.some(quote => quote.is_featured) && (
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                            Featured Quotes
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            {quotes.data.filter(quote => quote.is_featured).slice(0, 3).map((quote) => (
                                <Card key={quote.id} className="h-full hover:shadow-lg transition-shadow duration-200">
                                    <CardHeader className="pb-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <Badge variant="default" className="text-xs">
                                                <Heart className="h-3 w-3 mr-1" />
                                                Featured
                                            </Badge>
                                            {quote.category && (
                                                <Badge
                                                    variant="secondary"
                                                    className="text-xs"
                                                    style={{ backgroundColor: quote.category.color + '20', color: quote.category.color }}
                                                >
                                                    {quote.category.name}
                                                </Badge>
                                            )}
                                        </div>
                                        <blockquote className="text-sm italic text-gray-700 dark:text-gray-300 leading-relaxed">
                                            "{quote.text.length > 120 ? quote.text.substring(0, 120) + '...' : quote.text}"
                                        </blockquote>
                                    </CardHeader>

                                    <CardContent className="space-y-3 pt-0">
                                        {quote.reference && (
                                            <p className="text-xs font-semibold text-blue-600 dark:text-blue-400">
                                                {quote.reference}
                                            </p>
                                        )}

                                        <Link href={`/quotes/${quote.id}`}>
                                            <Button variant="outline" size="sm" className="w-full">
                                                <BookOpen className="h-4 w-4 mr-2" />
                                                Read Full Quote
                                            </Button>
                                        </Link>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}

                {/* All Quotes */}
                <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                        {safeFilters.search || safeFilters.category ? 'Search Results' : 'All Quotes'}
                    </h2>

                    {quotes.data && quotes.data.length > 0 ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {quotes.data.map((quote) => (
                                    <Card key={quote.id} className="h-full hover:shadow-lg transition-shadow duration-200">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center justify-between mb-2">
                                                {quote.category && (
                                                    <Badge
                                                        variant="secondary"
                                                        className="text-xs"
                                                        style={{ backgroundColor: quote.category.color + '20', color: quote.category.color }}
                                                    >
                                                        {quote.category.name}
                                                    </Badge>
                                                )}
                                                {quote.is_featured && (
                                                    <Badge variant="default" className="text-xs">
                                                        <Heart className="h-3 w-3 mr-1" />
                                                        Featured
                                                    </Badge>
                                                )}
                                            </div>
                                            <blockquote className="text-sm italic text-gray-700 dark:text-gray-300 leading-relaxed">
                                                "{quote.text.length > 120 ? quote.text.substring(0, 120) + '...' : quote.text}"
                                            </blockquote>
                                        </CardHeader>

                                        <CardContent className="space-y-3 pt-0">
                                            {quote.reference && (
                                                <p className="text-xs font-semibold text-blue-600 dark:text-blue-400">
                                                    {quote.reference}
                                                </p>
                                            )}

                                            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                                <div className="flex items-center">
                                                    <Calendar className="h-3 w-3 mr-1" />
                                                    {new Date(quote.created_at).toLocaleDateString()}
                                                </div>
                                            </div>

                                            {/* Download Indicators */}
                                            {(quote.pdf_path || quote.audio_path) && (
                                                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                                    <span>Available:</span>
                                                    {quote.pdf_path && (
                                                        <div className="flex items-center">
                                                            <FileText className="h-3 w-3 mr-1 text-red-500" />
                                                            PDF
                                                        </div>
                                                    )}
                                                    {quote.audio_path && (
                                                        <div className="flex items-center">
                                                            <Volume2 className="h-3 w-3 mr-1 text-blue-500" />
                                                            Audio
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            <div className="flex gap-2">
                                                <Link href={`/quotes/${quote.id}`} className="flex-1">
                                                    <Button variant="outline" size="sm" className="w-full">
                                                        <BookOpen className="h-4 w-4 mr-2" />
                                                        Read Full Quote
                                                    </Button>
                                                </Link>

                                                <Button
                                                    variant={quote.is_favorited ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => toggleFavorite(quote.id)}
                                                    disabled={favoriteLoading[quote.id]}
                                                    className="px-3"
                                                    title={quote.is_favorited ? "Remove from favorites" : "Add to favorites"}
                                                >
                                                    <Heart
                                                        className={`h-4 w-4 ${quote.is_favorited ? 'fill-current' : ''}`}
                                                    />
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>

                            {/* Pagination */}
                            {quotes.links && quotes.links.length > 3 && (
                                <div className="flex justify-center mt-8">
                                    <div className="flex space-x-2">
                                        {quotes.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`px-3 py-2 text-sm rounded-md ${link.active
                                                    ? 'bg-blue-600 text-white'
                                                    : link.url
                                                        ? 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600'
                                                        : 'bg-gray-100 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
                                                    }`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="text-center py-12">
                            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                No quotes found
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                {safeFilters.search || safeFilters.category
                                    ? 'Try adjusting your search or filter criteria.'
                                    : 'No quotes have been added yet.'}
                            </p>
                            {(safeFilters.search || safeFilters.category) && (
                                <Link href="/quotes">
                                    <Button variant="outline">
                                        Clear Filters
                                    </Button>
                                </Link>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </QuotesLayout>
    );
}