import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Tag } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

export default function AdminCategoryCreate({ auth }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        description: '',
        is_active: true,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('admin.categories.store'));
    };

    return (
        <AdminLayout>
            <Head title="Create Category - Admin Dashboard" />

            <div className="max-w-2xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>

                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Add a new category to organize quotes
                        </p>
                    </div>
                    <Link href="/admin/categories">
                        <Button variant="outline">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Categories
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Tag className="h-5 w-5" />
                            <span>Category Information</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Category Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Category Name *</Label>
                                <Input
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., Faith, Hope, Love"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                                <p className="text-sm text-gray-500">
                                    A unique name for this category. The URL slug will be generated automatically.
                                </p>
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Describe what types of quotes belong in this category..."
                                    rows={3}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                                <p className="text-sm text-gray-500">
                                    Optional description to help users understand this category.
                                </p>
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Active Category</Label>
                            </div>
                            <p className="text-sm text-gray-500 ml-6">
                                Active categories are visible to users and can be assigned to quotes.
                            </p>

                            {/* Submit Buttons */}
                            <div className="flex justify-end space-x-4 pt-6">
                                <Button type="button" variant="outline" onClick={() => reset()}>
                                    Reset
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? (
                                        <>
                                            <Save className="h-4 w-4 mr-2 animate-spin" />
                                            Creating...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4 mr-2" />
                                            Create Category
                                        </>
                                    )}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Help Card */}
                <Card>
                    <CardHeader>
                        <CardTitle>Category Guidelines</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Best Practices:</h4>
                            <ul className="space-y-1 list-disc list-inside">
                                <li>Use clear, descriptive names that users will understand</li>
                                <li>Keep category names concise (1-3 words when possible)</li>
                                <li>Avoid overlapping categories that might confuse users</li>
                                <li>Consider the spiritual themes that resonate with your audience</li>
                            </ul>
                        </div>

                        <div className="text-sm text-gray-600 dark:text-gray-400">
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Examples:</h4>
                            <ul className="space-y-1 list-disc list-inside">
                                <li><strong>Faith:</strong> Quotes about trust in God and spiritual belief</li>
                                <li><strong>Prayer:</strong> Quotes about communication with God</li>
                                <li><strong>Character:</strong> Quotes about Christian character development</li>
                                <li><strong>Prophecy:</strong> Quotes about end-time events and biblical prophecy</li>
                            </ul>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
