import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { <PERSON><PERSON>eft, BookOpen, Share2, RefreshCw, Loader2, Edit, Trash2, Download, FileText, Volume2, Play, Heart } from 'lucide-react';
import QuoteImageGenerator from '@/components/quote-image-generator';
import QuotesLayout from '@/layouts/quotes-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export default function QuoteShow({ quote, auth, isFavorited, statistics }) {
    const [isLoading, setIsLoading] = useState(false);
    const [favoriteLoading, setFavoriteLoading] = useState(false);
    const [isFavorite, setIsFavorite] = useState(isFavorited);

    const shareQuote = async () => {
        const shareData = {
            title: `Quote from ${quote.reference || 'SOP Quotes'}`,
            text: `"${quote.text}" ${quote.reference ? `- ${quote.reference}` : ''}`,
            url: window.location.href,
        };

        try {
            if (navigator.share) {
                await navigator.share(shareData);
            } else {
                await navigator.clipboard.writeText(`${shareData.text}\n\n${shareData.url}`);
                alert('Quote copied to clipboard!');
            }
        } catch (error) {
            console.error('Failed to share:', error);
            alert('Unable to share quote. Please try again.');
        }
    };



    const getNewQuote = async () => {
        setIsLoading(true);

        try {
            // Fetch a random quote ID from the API
            const response = await fetch('/api/quotes/random');
            const data = await response.json();

            if (data.quote && data.quote.id) {
                // Navigate to the new quote details page
                router.visit(`/quotes/${data.quote.id}`, {
                    preserveScroll: false,
                    onFinish: () => setIsLoading(false),
                });
            } else {
                throw new Error('No quote received');
            }
        } catch (error) {
            console.error('Error fetching new quote:', error);
            alert('Unable to load new quote. Please try again.');
            setIsLoading(false);
        }
    };

    const toggleFavorite = async () => {
        setFavoriteLoading(true);

        try {
            const response = await fetch(`/favorites/quote/${quote.id}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            });

            const data = await response.json();

            if (data.success) {
                setIsFavorite(data.is_favorited);
            } else {
                alert('Failed to update favorite status');
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            alert('Failed to update favorite status');
        } finally {
            setFavoriteLoading(false);
        }
    };

    return (
        <QuotesLayout>
            <Head title={`Quote - SOP Quotes`} />

            <div className="container mx-auto px-4 py-8 max-w-4xl">
                {/* Back Button */}
                <div className="mb-6">
                    <Link href="/quotes">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Quotes
                        </Button>
                    </Link>
                </div>

                {/* Main Quote Card */}
                <Card className="mb-8 shadow-lg">
                    <CardHeader className="text-center pb-8 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                        <div className="flex justify-center mb-4">
                            <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                        </div>
                        <blockquote className="text-2xl md:text-4xl italic text-gray-800 dark:text-gray-200 leading-relaxed font-light max-w-4xl mx-auto">
                            "{quote.text}"
                        </blockquote>
                    </CardHeader>

                    <CardContent className="space-y-6 p-8">
                        {/* Attribution */}
                        <div className="text-center space-y-3">
                            {quote.reference && (
                                <p className="text-xl font-bold text-blue-700 dark:text-blue-300">
                                    {quote.reference}
                                </p>
                            )}

                            {quote.author && (
                                <p className="text-lg text-gray-700 dark:text-gray-300 font-medium">
                                    — {quote.author}
                                </p>
                            )}

                            {quote.source && (
                                <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                                    Source: {quote.source}
                                </p>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-wrap justify-center gap-3 pt-4">
                            <Button
                                variant="outline"
                                onClick={getNewQuote}
                                disabled={isLoading}
                                className="min-w-[120px]"
                            >
                                {isLoading ? (
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                    <RefreshCw className="h-4 w-4 mr-2" />
                                )}
                                New Quote
                            </Button>

                            <Button variant="outline" onClick={shareQuote} className="min-w-[100px]">
                                <Share2 className="h-4 w-4 mr-2" />
                                Share
                            </Button>

                            <Button
                                variant={isFavorite ? "default" : "outline"}
                                onClick={toggleFavorite}
                                disabled={favoriteLoading}
                                className="min-w-[120px]"
                            >
                                <Heart
                                    className={`h-4 w-4 mr-2 ${isFavorite ? 'fill-current' : ''}`}
                                />
                                {isFavorite ? 'Favorited' : 'Add to Favorites'}
                            </Button>

                            <QuoteImageGenerator quote={quote} />
                        </div>

                        {/* Media Section */}
                        {(quote.pdf_path || quote.audio_path) && (
                            <div className="border-t pt-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
                                    Available Media
                                </h3>

                                {/* Audio Player */}
                                {quote.audio_path && (
                                    <div className="mb-6">
                                        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                                            🎵 Listen to Audio
                                        </h4>
                                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                                            <audio
                                                controls
                                                className="w-full"
                                                preload="metadata"
                                            >
                                                <source src={`/stream/quote/${quote.id}/audio`} type="audio/mpeg" />
                                                Your browser does not support the audio element.
                                            </audio>
                                            <div className="flex justify-center mt-3">
                                                <a
                                                    href={`/download/quote/${quote.id}/audio`}
                                                    className="inline-flex"
                                                >
                                                    <Button variant="outline" size="sm">
                                                        <Download className="h-4 w-4 mr-2" />
                                                        Download Audio
                                                    </Button>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* PDF Viewer */}
                                {quote.pdf_path && (
                                    <div className="mb-6">
                                        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                                            📄 View Document
                                        </h4>
                                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                                            <div className="aspect-[4/3] bg-white rounded border">
                                                <iframe
                                                    src={`/stream/quote/${quote.id}/pdf`}
                                                    className="w-full h-full rounded"
                                                    title="Quote PDF"
                                                />
                                            </div>
                                            <div className="flex justify-center mt-3">
                                                <a
                                                    href={`/download/quote/${quote.id}/pdf`}
                                                    className="inline-flex"
                                                >
                                                    <Button variant="outline" size="sm">
                                                        <Download className="h-4 w-4 mr-2" />
                                                        Download PDF
                                                    </Button>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Navigation */}
                        <div className="text-center pt-4 border-t border-gray-100 dark:border-gray-700 mt-6">
                            <Link href="/quotes">
                                <Button variant="outline">
                                    <BookOpen className="h-4 w-4 mr-2" />
                                    Browse More Quotes
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </QuotesLayout>
    );
}
