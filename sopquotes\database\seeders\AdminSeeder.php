<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user using the regular User model
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'SOP Quotes Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        echo "✅ Admin user created successfully!\n";
        echo "📧 Email: <EMAIL>\n";
        echo "🔑 Password: admin123\n";
        echo "⚠️  Please change the password after first login!\n";
        echo "🔐 This user has admin access to manage the system.\n";
    }
}
