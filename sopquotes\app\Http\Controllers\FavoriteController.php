<?php

namespace App\Http\Controllers;

use App\Models\Quote;
use App\Models\UserFavorite;
use Illuminate\Http\Request;

class FavoriteController extends Controller
{
    /**
     * Toggle favorite status for a quote.
     */
    public function toggle(Request $request, Quote $quote)
    {
        $user = auth()->user();

        if ($user) {
            // Authenticated user - use database favorites
            $favorite = UserFavorite::where([
                'user_id' => $user->id,
                'favoritable_type' => Quote::class,
                'favoritable_id' => $quote->id,
            ])->first();

            if ($favorite) {
                // Remove from favorites
                $favorite->delete();
                $isFavorited = false;
                $message = 'Quote removed from favorites';
            } else {
                // Add to favorites
                UserFavorite::create([
                    'user_id' => $user->id,
                    'favoritable_type' => Quote::class,
                    'favoritable_id' => $quote->id,
                ]);
                $isFavorited = true;
                $message = 'Quote added to favorites';
            }
        } else {
            // Guest user - use session-based favorites
            $sessionFavorites = session()->get('favorites', []);

            if (in_array($quote->id, $sessionFavorites)) {
                // Remove from favorites
                $sessionFavorites = array_diff($sessionFavorites, [$quote->id]);
                $isFavorited = false;
                $message = 'Quote removed from favorites';
            } else {
                // Add to favorites
                $sessionFavorites[] = $quote->id;
                $isFavorited = true;
                $message = 'Quote added to favorites';
            }

            session()->put('favorites', array_values($sessionFavorites));
        }

        return response()->json([
            'success' => true,
            'is_favorited' => $isFavorited,
            'message' => $message,
        ]);
    }
}
