<?php

namespace App\Http\Controllers;

use App\Models\Quote;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FavoritesController extends Controller
{
    /**
     * Display a listing of user's favorite quotes.
     */
    public function index(Request $request): Response
    {
        $query = Quote::with(['category', 'creator'])
            ->active()
            ->latest();

        // Filter by user's favorites
        if (auth()->check()) {
            // Authenticated users - use database favorites
            $query->whereHas('favorites', function ($q) {
                $q->where('user_id', auth()->id());
            });
        } else {
            // Guest users - use session favorites
            $sessionFavorites = session()->get('favorites', []);
            if (!empty($sessionFavorites)) {
                $query->whereIn('id', $sessionFavorites);
            } else {
                // No session favorites, return empty result
                $query->where('id', -1);
            }
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $quotes = $query->paginate(12)->withQueryString();

        // Add favorite status for all users (will always be true on this page)
        if (auth()->check()) {
            // Authenticated users - check database favorites
            $quotes->getCollection()->transform(function ($quote) {
                $quote->is_favorited = true; // Always true on favorites page
                return $quote;
            });
        } else {
            // Guest users - check session favorites
            $quotes->getCollection()->transform(function ($quote) {
                $quote->is_favorited = true; // Always true on favorites page
                return $quote;
            });
        }

        $categories = Category::active()->ordered()->get();

        // Debug: Log categories to see what's being fetched
        \Log::info('Categories fetched for favorites index:', [
            'count' => $categories->count(),
            'categories' => $categories->pluck('name', 'id')->toArray()
        ]);

        return Inertia::render('favorites/index', [
            'quotes' => $quotes,
            'categories' => $categories,
            'filters' => (object) $request->only(['category', 'search']),
        ]);
    }
}
