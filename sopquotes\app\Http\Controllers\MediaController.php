<?php

namespace App\Http\Controllers;

use App\Models\Media;
use App\Models\Category;
use App\Models\UserDownload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class MediaController extends Controller
{
    /**
     * Display a listing of active media files for public access.
     */
    public function index(Request $request)
    {
        $query = Media::with(['category'])
            ->where('is_active', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('file_type')) {
            $query->where('file_type', $request->file_type);
        }

        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        $media = $query->paginate(12)->withQueryString();

        return Inertia::render('media/index', [
            'media' => $media,
            'categories' => Category::where('is_active', true)->get(),
            'filters' => (object) $request->only(['search', 'file_type', 'category']),
            'auth' => auth()->user(),
        ]);
    }

    /**
     * Display the specified media file.
     */
    public function show(Media $media)
    {
        // Only show active media files to public
        if (!$media->is_active) {
            abort(404);
        }

        $media->load(['category', 'uploader']);
        
        // Increment view count
        $media->increment('view_count');

        return Inertia::render('media/show', [
            'media' => $media,
            'auth' => auth()->user(),
        ]);
    }

    /**
     * Download a media file.
     */
    public function download(Media $media)
    {
        // Only allow downloads of active media files
        if (!$media->is_active) {
            abort(404);
        }

        if (!$media->file_path || !Storage::disk('public')->exists($media->file_path)) {
            abort(404, 'File not found.');
        }

        // Track the download
        $this->trackDownload($media);

        $filePath = Storage::disk('public')->path($media->file_path);
        $fileName = $this->generateFileName($media);

        return response()->download($filePath, $fileName);
    }

    /**
     * Stream a media file for inline viewing/listening.
     */
    public function stream(Media $media)
    {
        // Only allow streaming of active media files
        if (!$media->is_active) {
            abort(404);
        }

        if (!$media->file_path || !Storage::disk('public')->exists($media->file_path)) {
            abort(404, 'File not found.');
        }

        $filePath = Storage::disk('public')->path($media->file_path);
        
        $headers = [
            'Accept-Ranges' => 'bytes',
        ];

        if ($media->file_type === 'audio') {
            $headers['Content-Type'] = $this->getAudioMimeType($media->file_path);
        } elseif ($media->file_type === 'pdf') {
            $headers['Content-Type'] = 'application/pdf';
            $headers['Content-Disposition'] = 'inline; filename="' . $this->generateFileName($media) . '"';
        }

        return response()->file($filePath, $headers);
    }

    /**
     * Track a download in the database.
     */
    private function trackDownload($media)
    {
        // Only track downloads for authenticated users
        // Anonymous users can still download, but we don't track them for privacy
        if (auth()->check()) {
            UserDownload::create([
                'user_id' => auth()->id(),
                'downloadable_type' => get_class($media),
                'downloadable_id' => $media->id,
                'file_type' => $media->file_type,
                'downloaded_at' => now(),
            ]);
        }
    }

    /**
     * Generate a user-friendly filename for downloads.
     */
    private function generateFileName($media)
    {
        $extension = $this->getFileExtension($media->file_path);

        // Create a clean filename from media title
        $title = substr($media->title, 0, 50);
        $cleanTitle = preg_replace('/[^a-zA-Z0-9\s]/', '', $title);
        $cleanTitle = preg_replace('/\s+/', '-', trim($cleanTitle));
        $cleanTitle = strtolower($cleanTitle);

        // Add media ID for uniqueness
        return "sop-media-{$media->id}-{$cleanTitle}.{$extension}";
    }

    /**
     * Get the file extension.
     */
    private function getFileExtension($filePath)
    {
        return pathinfo($filePath, PATHINFO_EXTENSION);
    }

    /**
     * Get the MIME type for audio files.
     */
    private function getAudioMimeType($filePath)
    {
        $extension = $this->getFileExtension($filePath);

        return match ($extension) {
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'm4a' => 'audio/mp4',
            default => 'audio/mpeg',
        };
    }
}
