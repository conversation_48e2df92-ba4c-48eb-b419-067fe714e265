import{r as p,m as C,j as e,L as b,$ as w}from"./app-BWHLaRS2.js";import{A as S}from"./admin-layout-DKXm94gZ.js";import{B as n}from"./button-CUovRkQ3.js";import{C as k,c as T}from"./card-3ac6awgC.js";import{I as d}from"./input-B4rX1XfI.js";import{L as o}from"./label-DWihlnjb.js";import{T as A,S as F}from"./textarea-DsrDZwdm.js";import{C as _}from"./checkbox-B6mGW3Gq.js";import{B as Q}from"./badge-BSmw6aR2.js";import{A as L}from"./arrow-left-B-2ALS0F.js";import{P as q}from"./plus-CzNwqLHA.js";import{X as B}from"./flash-message-BOB8jroJ.js";import{U as E}from"./upload-Cb6Ub9xq.js";import"./sun-Bmefm2yw.js";import"./book-open-DxiZ0b6m.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./index-BDjL_iy4.js";function oe({categories:j,auth:D}){const[i,m]=p.useState([]),[c,x]=p.useState(""),{data:a,setData:r,post:g,processing:h,errors:t,reset:f}=C({text:"",source:"",author:"",reference:"",category_id:"",tags:[],is_featured:!1}),N=s=>{s.preventDefault(),g(route("admin.quotes.store"),{data:{...a,tags:JSON.stringify(i)}})},u=()=>{if(c.trim()&&!i.includes(c.trim())){const s=[...i,c.trim()];m(s),r("tags",s),x("")}},v=s=>{const l=i.filter(y=>y!==s);m(l),r("tags",l)};return e.jsxs(S,{children:[e.jsx(b,{title:"Create Quote - Admin Dashboard"}),e.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create New Quote"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Add a new inspirational quote to the SOP Quotes collection"})]}),e.jsx(w,{href:"/admin/quotes",children:e.jsxs(n,{variant:"outline",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Back to Quotes"]})})]}),e.jsx(k,{children:e.jsx(T,{className:"p-6",children:e.jsxs("form",{onSubmit:N,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"text",children:"Quote Text *"}),e.jsx(A,{id:"text",value:a.text,onChange:s=>r("text",s.target.value),placeholder:"Enter the inspirational quote...",rows:4,className:t.text?"border-red-500":""}),t.text&&e.jsx("p",{className:"text-red-500 text-sm",children:t.text})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"reference",children:"Reference"}),e.jsx(d,{id:"reference",value:a.reference,onChange:s=>r("reference",s.target.value),placeholder:"e.g., Steps to Christ, p. 123",className:t.reference?"border-red-500":""}),t.reference&&e.jsx("p",{className:"text-red-500 text-sm",children:t.reference})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"author",children:"Author"}),e.jsx(d,{id:"author",value:a.author,onChange:s=>r("author",s.target.value),placeholder:"e.g., Ellen G. White",className:t.author?"border-red-500":""}),t.author&&e.jsx("p",{className:"text-red-500 text-sm",children:t.author})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"source",children:"Source"}),e.jsx(d,{id:"source",value:a.source,onChange:s=>r("source",s.target.value),placeholder:"e.g., Steps to Christ",className:t.source?"border-red-500":""}),t.source&&e.jsx("p",{className:"text-red-500 text-sm",children:t.source})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"category",children:"Category"}),e.jsxs("select",{id:"category",value:a.category_id,onChange:s=>r("category_id",s.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${t.category_id?"border-red-500":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select a category"}),j.map(s=>e.jsx("option",{value:s.id.toString(),children:s.name},s.id))]}),t.category_id&&e.jsx("p",{className:"text-red-500 text-sm",children:t.category_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"tags",children:"Tags"}),e.jsxs("div",{className:"flex gap-2 mb-2",children:[e.jsx(d,{value:c,onChange:s=>x(s.target.value),placeholder:"Add a tag...",onKeyPress:s=>s.key==="Enter"&&(s.preventDefault(),u())}),e.jsx(n,{type:"button",onClick:u,variant:"outline",size:"sm",children:e.jsx(q,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:i.map((s,l)=>e.jsxs(Q,{variant:"secondary",className:"flex items-center gap-1",children:[s,e.jsx(B,{className:"h-3 w-3 cursor-pointer",onClick:()=>v(s)})]},l))}),t.tags&&e.jsx("p",{className:"text-red-500 text-sm",children:t.tags})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{id:"featured",checked:a.is_featured,onCheckedChange:s=>r("is_featured",s)}),e.jsx(o,{htmlFor:"featured",children:"Mark as featured quote"})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(n,{type:"button",variant:"outline",onClick:()=>f(),children:"Reset"}),e.jsx(n,{type:"submit",disabled:h,children:h?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Create Quote"]})})]})]})})})]})]})}export{oe as default};
