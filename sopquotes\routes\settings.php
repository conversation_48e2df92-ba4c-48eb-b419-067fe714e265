<?php

use App\Http\Controllers\Settings\ProfileController;
use App\Http\Controllers\Settings\PasswordController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public settings pages - accessible to everyone (no user accounts)
Route::get('settings', function () {
    return Inertia::render('settings/index', [
        'appVersion' => config('app.version', '1.0.0')
    ]);
})->name('settings.index');

Route::get('settings/appearance', function () {
    return Inertia::render('settings/appearance', [
        'appVersion' => config('app.version', '1.0.0')
    ]);
})->name('appearance');

// Admin profile and password routes - require authentication
Route::middleware('auth')->group(function () {
    Route::get('profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('password', [PasswordController::class, 'edit'])->name('password.edit');
    Route::patch('password', [PasswordController::class, 'update'])->name('password.update');
});
