<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Quote;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class QuoteController extends Controller
{
    /**
     * Display a listing of quotes for admin.
     */
    public function index(Request $request): Response
    {
        // Authorization is handled by admin middleware

        $query = Quote::with(['category', 'creator'])
            ->latest();

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'featured':
                    $query->featured();
                    break;
                case 'active':
                    $query->active();
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
            }
        }

        $quotes = $query->paginate(10)->withQueryString();

        return Inertia::render('admin/quotes/index', [
            'quotes' => $quotes,
            'categories' => Category::active()->ordered()->get(),
            'filters' => (object) $request->only(['category', 'search', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new quote.
     */
    public function create(): Response
    {
        // Authorization is handled by admin middleware

        return Inertia::render('admin/quotes/create', [
            'categories' => Category::active()->ordered()->get(),
        ]);
    }

    /**
     * Store a newly created quote in storage.
     */
    public function store(Request $request)
    {
        // Authorization is handled by admin middleware

        $validated = $request->validate([
            'text' => 'required|string|max:1000',
            'source' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'reference' => 'nullable|string|max:255',
            'category_id' => 'nullable|exists:categories,id',
            'tags' => 'nullable|string', // JSON string from frontend
            'is_featured' => 'boolean',

        ]);

        // Parse tags JSON
        if ($validated['tags']) {
            $validated['tags'] = json_decode($validated['tags'], true) ?: [];
        }

        $quote = Quote::create(array_merge($validated, [
            'created_by' => auth()->id(),
        ]));

        return redirect()->route('admin.quotes.index')
            ->with('success', 'Quote created successfully.');
    }

    /**
     * Show the form for editing the specified quote.
     */
    public function edit(Quote $quote): Response
    {
        // Authorization is handled by admin middleware

        return Inertia::render('admin/quotes/edit', [
            'quote' => $quote,
            'categories' => Category::active()->ordered()->get(),
        ]);
    }

    /**
     * Update the specified quote in storage.
     */
    public function update(Request $request, Quote $quote)
    {
        // Authorization is handled by admin middleware

        $validated = $request->validate([
            'text' => 'required|string|max:1000',
            'source' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'reference' => 'nullable|string|max:255',
            'category_id' => 'nullable|exists:categories,id',
            'tags' => 'nullable|string', // JSON string from frontend
            'is_featured' => 'boolean',

        ]);

        // Parse tags JSON
        if ($validated['tags']) {
            $validated['tags'] = json_decode($validated['tags'], true) ?: [];
        }

        $quote->update($validated);

        return redirect()->route('admin.quotes.index')
            ->with('success', 'Quote updated successfully.');
    }

    /**
     * Remove the specified quote from storage.
     */
    public function destroy(Quote $quote)
    {
        // Authorization is handled by admin middleware

        $quote->delete();

        return redirect()->route('admin.quotes.index')
            ->with('success', 'Quote deleted successfully.');
    }

    /**
     * Toggle featured status for a quote.
     */
    public function toggleFeatured(Quote $quote)
    {
        // Authorization is handled by admin middleware

        $quote->update([
            'is_featured' => !$quote->is_featured
        ]);

        return back()->with(
            'success',
            $quote->is_featured ? 'Quote marked as featured.' : 'Quote removed from featured.'
        );
    }
}
