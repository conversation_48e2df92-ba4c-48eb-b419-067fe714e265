<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_downloads', function (Blueprint $table) {
            $table->string('file_type')->nullable()->after('downloadable_id'); // pdf, audio, etc.
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_downloads', function (Blueprint $table) {
            $table->dropColumn('file_type');
        });
    }
};
