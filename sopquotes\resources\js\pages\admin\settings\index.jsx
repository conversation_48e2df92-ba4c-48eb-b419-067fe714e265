import { Head, useForm } from '@inertiajs/react';
import { useState } from 'react';
import { Save, RotateCcw, Settings, Globe, Mail, Palette, AlertTriangle } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function AdminSettingsIndex({ settings, auth }) {
    const [activeTab, setActiveTab] = useState('general');

    // Convert settings object to array format for form
    const settingsArray = Object.entries(settings).flatMap(([group, groupSettings]) =>
        groupSettings.map(setting => ({
            ...setting,
            group
        }))
    );

    const { data, setData, patch, processing, errors, reset } = useForm({
        settings: settingsArray,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        patch(route('admin.settings.update'));
    };

    const handleReset = (group) => {
        if (confirm(`Are you sure you want to reset all ${group} settings to defaults? This action cannot be undone.`)) {
            patch(route('admin.settings.reset'), {
                data: { group },
                preserveScroll: true,
            });
        }
    };

    const updateSetting = (key, field, value) => {
        const newSettings = data.settings.map(setting =>
            setting.key === key ? { ...setting, [field]: value } : setting
        );
        setData('settings', newSettings);
    };

    const getSettingsByGroup = (group) => {
        return data.settings.filter(setting => setting.group === group);
    };

    const renderSettingField = (setting) => {
        const commonProps = {
            id: setting.key,
            className: errors[`settings.${setting.key}`] ? 'border-red-500' : '',
        };

        switch (setting.type) {
            case 'boolean':
                return (
                    <Checkbox
                        {...commonProps}
                        checked={setting.value === '1' || setting.value === true}
                        onCheckedChange={(checked) => updateSetting(setting.key, 'value', checked ? '1' : '0')}
                    />
                );
            case 'integer':
                return (
                    <Input
                        {...commonProps}
                        type="number"
                        value={setting.value || ''}
                        onChange={(e) => updateSetting(setting.key, 'value', e.target.value)}
                    />
                );
            case 'json':
                return (
                    <Textarea
                        {...commonProps}
                        value={typeof setting.value === 'object' ? JSON.stringify(setting.value, null, 2) : setting.value || ''}
                        onChange={(e) => updateSetting(setting.key, 'value', e.target.value)}
                        rows={4}
                    />
                );
            default:
                return setting.key.includes('description') || setting.key.includes('content') ? (
                    <Textarea
                        {...commonProps}
                        value={setting.value || ''}
                        onChange={(e) => updateSetting(setting.key, 'value', e.target.value)}
                        rows={3}
                    />
                ) : (
                    <Input
                        {...commonProps}
                        type={setting.key.includes('email') ? 'email' : setting.key.includes('time') ? 'time' : 'text'}
                        value={setting.value || ''}
                        onChange={(e) => updateSetting(setting.key, 'value', e.target.value)}
                    />
                );
        }
    };

    const getGroupIcon = (group) => {
        switch (group) {
            case 'general': return Globe;
            case 'email': return Mail;
            case 'appearance': return Palette;
            default: return Settings;
        }
    };

    const getGroupTitle = (group) => {
        return group.charAt(0).toUpperCase() + group.slice(1) + ' Settings';
    };

    return (
        <AdminLayout>
            <Head title="System Settings - Admin Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">System Settings</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                        Configure your SOP Quotes application settings
                    </p>
                </div>

                <form onSubmit={handleSubmit}>
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="general" className="flex items-center space-x-2">
                                <Globe className="h-4 w-4" />
                                <span>General</span>
                            </TabsTrigger>
                            <TabsTrigger value="email" className="flex items-center space-x-2">
                                <Mail className="h-4 w-4" />
                                <span>Email</span>
                            </TabsTrigger>
                            <TabsTrigger value="appearance" className="flex items-center space-x-2">
                                <Palette className="h-4 w-4" />
                                <span>Appearance</span>
                            </TabsTrigger>
                        </TabsList>

                        {Object.keys(settings).map((group) => {
                            const GroupIcon = getGroupIcon(group);
                            const groupSettings = getSettingsByGroup(group);

                            return (
                                <TabsContent key={group} value={group} className="space-y-6">
                                    <Card>
                                        <CardHeader className="flex flex-row items-center justify-between">
                                            <CardTitle className="flex items-center space-x-2">
                                                <GroupIcon className="h-5 w-5" />
                                                <span>{getGroupTitle(group)}</span>
                                            </CardTitle>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleReset(group)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <RotateCcw className="h-4 w-4 mr-2" />
                                                Reset to Defaults
                                            </Button>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            {groupSettings.map((setting) => (
                                                <div key={setting.key} className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor={setting.key} className="text-sm font-medium">
                                                            {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                        </Label>
                                                        {setting.is_public && (
                                                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                                                Public
                                                            </span>
                                                        )}
                                                    </div>

                                                    {setting.type === 'boolean' ? (
                                                        <div className="flex items-center space-x-2">
                                                            {renderSettingField(setting)}
                                                            <Label htmlFor={setting.key} className="text-sm text-gray-600">
                                                                {setting.description}
                                                            </Label>
                                                        </div>
                                                    ) : (
                                                        <>
                                                            {renderSettingField(setting)}
                                                            {setting.description && (
                                                                <p className="text-sm text-gray-500">{setting.description}</p>
                                                            )}
                                                        </>
                                                    )}

                                                    {errors[`settings.${setting.key}`] && (
                                                        <p className="text-red-500 text-sm">{errors[`settings.${setting.key}`]}</p>
                                                    )}
                                                </div>
                                            ))}
                                        </CardContent>
                                    </Card>
                                </TabsContent>
                            );
                        })}
                    </Tabs>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4 mt-6">
                        <Button type="button" variant="outline" onClick={() => reset()}>
                            Reset Changes
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? (
                                <>
                                    <Save className="h-4 w-4 mr-2 animate-spin" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Settings
                                </>
                            )}
                        </Button>
                    </div>
                </form>

                {/* Warning Card */}
                <Card className="border-yellow-200 dark:border-yellow-800">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2 text-yellow-600 dark:text-yellow-400">
                            <AlertTriangle className="h-5 w-5" />
                            <span>Important Notes</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <p>• Settings marked as "Public" are accessible to the frontend and will be cached for performance.</p>
                        <p>• Email settings require proper SMTP configuration in your environment file.</p>
                        <p>• Changes to appearance settings may require users to refresh their browsers.</p>
                        <p>• Resetting settings to defaults will permanently delete current values.</p>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
