import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Search, Plus, Edit, Trash2, Eye, Tag, ToggleLeft, ToggleRight } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';


export default function AdminCategoriesIndex({ categories = { data: [], total: 0, links: [] }, filters = {}, auth }) {
    // Ensure filters is an object, not an array
    const safeFilters = Array.isArray(filters) ? {} : filters;

    const [searchTerm, setSearchTerm] = useState(String(safeFilters.search || ''));

    const [statusFilter, setStatusFilter] = useState(String(safeFilters.status || ''));



    const handleSearch = (e) => {
        e.preventDefault();
        router.get('/admin/categories', {
            search: searchTerm,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleToggleStatus = (category) => {
        const action = category.is_active ? 'deactivate' : 'activate';
        if (confirm(`Are you sure you want to ${action} the category "${category.name}"?`)) {
            router.patch(`/admin/categories/${category.id}/toggle-status`);
        }
    };

    const handleDelete = (category) => {
        if (category.quotes_count > 0) {
            alert(`Cannot delete "${category.name}" because it has ${category.quotes_count} quotes. Please move or delete the quotes first.`);
            return;
        }

        if (confirm(`Are you sure you want to delete the category "${category.name}"? This action cannot be undone.`)) {
            router.delete(`/admin/categories/${category.id}`);
        }
    };

    return (
        <AdminLayout>
            <Head title="Manage Categories - Admin Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>

                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Create and manage quote categories
                        </p>
                    </div>
                    <Link href="/admin/categories/create">
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Category
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Input
                                        type="text"
                                        placeholder="Search categories..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <select
                                        value={statusFilter}
                                        onChange={(e) => setStatusFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>

                                <Button type="submit">
                                    <Search className="h-4 w-4 mr-2" />
                                    Search
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Categories List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Categories ({categories?.total || 0})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {categories?.data?.length > 0 ? categories.data.map((category) => (
                                <div key={category.id} className="border rounded-lg p-6">
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-3 mb-2">
                                                <Tag className="h-5 w-5 text-blue-500" />
                                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    {category.name}
                                                </h3>
                                                <Badge variant={category.is_active ? "default" : "secondary"}>
                                                    {category.is_active ? 'Active' : 'Inactive'}
                                                </Badge>
                                                <Badge variant="outline">
                                                    {category.quotes_count} quotes
                                                </Badge>
                                            </div>

                                            {category.description && (
                                                <p className="text-gray-600 dark:text-gray-400 mb-3">
                                                    {category.description}
                                                </p>
                                            )}

                                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                                <p><strong>Slug:</strong> {category.slug}</p>
                                                <p><strong>Created:</strong> {category.created_at ? new Date(category.created_at).toLocaleDateString() : 'Unknown'}</p>
                                                {category.updated_at && category.updated_at !== category.created_at && (
                                                    <p><strong>Updated:</strong> {new Date(category.updated_at).toLocaleDateString()}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="flex flex-col space-y-2 ml-4">
                                            <Link href={`/quotes?category=${category.slug}`}>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    View Quotes
                                                </Button>
                                            </Link>

                                            <Link href={`/admin/categories/${category.id}/edit`}>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    <Edit className="h-4 w-4 mr-2" />
                                                    Edit
                                                </Button>
                                            </Link>

                                            <Button
                                                variant={category.is_active ? "outline" : "default"}
                                                size="sm"
                                                onClick={() => handleToggleStatus(category)}
                                                className="w-full"
                                            >
                                                {category.is_active ? (
                                                    <>
                                                        <ToggleLeft className="h-4 w-4 mr-2" />
                                                        Deactivate
                                                    </>
                                                ) : (
                                                    <>
                                                        <ToggleRight className="h-4 w-4 mr-2" />
                                                        Activate
                                                    </>
                                                )}
                                            </Button>

                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleDelete(category)}
                                                className="w-full"
                                                disabled={category.quotes_count > 0}
                                            >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">No categories found</p>
                                    <Link href="/admin/categories/create" className="mt-4 inline-block">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Create Your First Category
                                        </Button>
                                    </Link>
                                </div>
                            )}

                            {/* Remove duplicate empty state */}
                            {false && (
                                <div className="text-center py-12">
                                    <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-500 text-lg">No categories found</p>
                                    <Link href="/admin/categories/create" className="mt-4 inline-block">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Create Your First Category
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </div>

                        {/* Pagination */}
                        {categories.links && (
                            <div className="flex justify-center mt-6">
                                <div className="flex space-x-2">
                                    {categories.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            className={`px-3 py-2 text-sm rounded-md ${link.active
                                                ? 'bg-blue-600 text-white'
                                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
