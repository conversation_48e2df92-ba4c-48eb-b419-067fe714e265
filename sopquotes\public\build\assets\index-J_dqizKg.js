import{r as x,j as e,L as C,$ as r,S as l}from"./app-BWHLaRS2.js";import{A as _}from"./admin-layout-DKXm94gZ.js";import{B as a}from"./button-CUovRkQ3.js";import{C as h,a as u,b as j,c as p}from"./card-3ac6awgC.js";import{B as f}from"./badge-BSmw6aR2.js";import{I as T}from"./input-B4rX1XfI.js";import{P as v}from"./plus-CzNwqLHA.js";import{S as $}from"./search-H5umLGXz.js";import{T as k}from"./tag-CF10xzlC.js";import{E as A}from"./eye-CbhFpv9_.js";import{S as q}from"./square-pen-BLfc3BBH.js";import{c as g}from"./book-open-DxiZ0b6m.js";import{T as D}from"./trash-2-DpyI9JiF.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./sun-Bmefm2yw.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"8",cy:"12",r:"2",key:"1nvbw3"}]],F=g("ToggleLeft",L);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"16",cy:"12",r:"2",key:"4ma0v8"}]],E=g("ToggleRight",I);function re({categories:t={data:[],total:0,links:[]},filters:n={},auth:z}){var m;const c=Array.isArray(n)?{}:n,[d,N]=x.useState(String(c.search||"")),[o,w]=x.useState(String(c.status||"")),b=s=>{s.preventDefault(),l.get("/admin/categories",{search:d,status:o},{preserveState:!0,preserveScroll:!0})},y=s=>{const i=s.is_active?"deactivate":"activate";confirm(`Are you sure you want to ${i} the category "${s.name}"?`)&&l.patch(`/admin/categories/${s.id}/toggle-status`)},S=s=>{if(s.quotes_count>0){alert(`Cannot delete "${s.name}" because it has ${s.quotes_count} quotes. Please move or delete the quotes first.`);return}confirm(`Are you sure you want to delete the category "${s.name}"? This action cannot be undone.`)&&l.delete(`/admin/categories/${s.id}`)};return e.jsxs(_,{children:[e.jsx(C,{title:"Manage Categories - Admin Dashboard"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Manage Categories"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Create and manage quote categories"})]}),e.jsx(r,{href:"/admin/categories/create",children:e.jsxs(a,{children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Create Category"]})})]}),e.jsxs(h,{children:[e.jsx(u,{children:e.jsx(j,{children:"Filters"})}),e.jsx(p,{children:e.jsx("form",{onSubmit:b,className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx("div",{children:e.jsx(T,{type:"text",placeholder:"Search categories...",value:d,onChange:s=>N(s.target.value)})}),e.jsx("div",{children:e.jsxs("select",{value:o,onChange:s=>w(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]})}),e.jsxs(a,{type:"submit",children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Search"]})]})})})]}),e.jsxs(h,{children:[e.jsx(u,{children:e.jsxs(j,{children:["Categories (",(t==null?void 0:t.total)||0,")"]})}),e.jsxs(p,{children:[e.jsxs("div",{className:"space-y-4",children:[((m=t==null?void 0:t.data)==null?void 0:m.length)>0?t.data.map(s=>e.jsx("div",{className:"border rounded-lg p-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[e.jsx(k,{className:"h-5 w-5 text-blue-500"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s.name}),e.jsx(f,{variant:s.is_active?"default":"secondary",children:s.is_active?"Active":"Inactive"}),e.jsxs(f,{variant:"outline",children:[s.quotes_count," quotes"]})]}),s.description&&e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-3",children:s.description}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Slug:"})," ",s.slug]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Created:"})," ",s.created_at?new Date(s.created_at).toLocaleDateString():"Unknown"]}),s.updated_at&&s.updated_at!==s.created_at&&e.jsxs("p",{children:[e.jsx("strong",{children:"Updated:"})," ",new Date(s.updated_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"flex flex-col space-y-2 ml-4",children:[e.jsx(r,{href:`/quotes?category=${s.slug}`,children:e.jsxs(a,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),"View Quotes"]})}),e.jsx(r,{href:`/admin/categories/${s.id}/edit`,children:e.jsxs(a,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Edit"]})}),e.jsx(a,{variant:s.is_active?"outline":"default",size:"sm",onClick:()=>y(s),className:"w-full",children:s.is_active?e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Deactivate"]}):e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),"Activate"]})}),e.jsxs(a,{variant:"destructive",size:"sm",onClick:()=>S(s),className:"w-full",disabled:s.quotes_count>0,children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})},s.id)):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("p",{className:"text-gray-500 text-lg",children:"No categories found"}),e.jsx(r,{href:"/admin/categories/create",className:"mt-4 inline-block",children:e.jsxs(a,{children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Create Your First Category"]})})]}),!1]}),t.links&&e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("div",{className:"flex space-x-2",children:t.links.map((s,i)=>e.jsx(r,{href:s.url||"#",className:`px-3 py-2 text-sm rounded-md ${s.active?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"} ${s.url?"":"opacity-50 cursor-not-allowed"}`,dangerouslySetInnerHTML:{__html:s.label}},i))})})]})]})]})]})}export{re as default};
