import{K as n,j as e,$ as s}from"./app-BWHLaRS2.js";import{B as a}from"./button-CUovRkQ3.js";import{D as d,a as l,b as i,c as r,F as o}from"./flash-message-BOB8jroJ.js";import{B as x}from"./book-open-DxiZ0b6m.js";import{U as c}from"./user-CmroWLXK.js";import{M as m}from"./menu-DfGfLdRU.js";function p({children:h}){const{auth:t}=n().props;return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(s,{href:"/",className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600",children:e.jsx(x,{className:"h-5 w-5 text-white"})}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Lapota SOP"})]}),e.jsxs("nav",{className:"hidden md:flex items-center space-x-8",children:[e.jsx(s,{href:"/",className:"text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 font-medium",children:"Home"}),e.jsx(s,{href:"/quotes",className:"text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 font-medium",children:"Browse Quotes"}),e.jsx(s,{href:"/quotes/daily",className:"text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 font-medium",children:"Daily Quote"}),t.user&&e.jsx(s,{href:"/dashboard",className:"text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 font-medium",children:"Dashboard"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[t.user?e.jsxs(d,{children:[e.jsx(l,{asChild:!0,children:e.jsxs(a,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:t.user.name})]})}),e.jsxs(i,{align:"end",children:[e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/dashboard",children:"Dashboard"})}),e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/settings",children:"Settings"})}),e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/logout",method:"post",children:"Logout"})})]})]}):e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(s,{href:"/login",children:e.jsx(a,{variant:"ghost",size:"sm",children:"Login"})}),e.jsx(s,{href:"/register",children:e.jsx(a,{size:"sm",children:"Register"})})]}),e.jsx("div",{className:"md:hidden",children:e.jsxs(d,{children:[e.jsx(l,{asChild:!0,children:e.jsx(a,{variant:"ghost",size:"sm",children:e.jsx(m,{className:"h-4 w-4"})})}),e.jsxs(i,{align:"end",children:[e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/",children:"Home"})}),e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/quotes",children:"Browse Quotes"})}),e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/quotes/daily",children:"Daily Quote"})}),t.user&&e.jsx(r,{asChild:!0,children:e.jsx(s,{href:"/dashboard",children:"Dashboard"})})]})]})})]})]})})}),e.jsx("main",{className:"flex-1",children:h}),e.jsx("footer",{className:"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4 md:mb-0",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600",children:e.jsx(x,{className:"h-4 w-4 text-white"})}),e.jsx("span",{className:"font-semibold text-gray-900 dark:text-white",children:"SOP Quotes"})]}),e.jsxs("div",{className:"flex space-x-6",children:[e.jsx(s,{href:"/",className:"text-sm text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"Home"}),e.jsx(s,{href:"/quotes",className:"text-sm text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"Quotes"}),e.jsx(s,{href:"/quotes/daily",className:"text-sm text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"Daily Quote"}),t.user&&e.jsx(s,{href:"/dashboard",className:"text-sm text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"Dashboard"})]})]}),e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"© 2025 LK Soft Development. Spreading God's love through His Word."})})]})}),e.jsx(o,{})]})}export{p as Q};
