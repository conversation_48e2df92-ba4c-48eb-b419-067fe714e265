<?php

use App\Http\Controllers\QuoteController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\FavoriteController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Public Quotes routes - accessible to everyone (viewing only)
Route::get('/quotes', [QuoteController::class, 'index'])->name('quotes.index');
Route::get('/quotes/daily', [QuoteController::class, 'daily'])->name('quotes.daily');
Route::get('/quotes/{quote}', [QuoteController::class, 'show'])->name('quotes.show');

// Public Media routes - accessible to everyone
Route::get('/media', [MediaController::class, 'index'])->name('media.index');
Route::get('/media/{media}', [MediaController::class, 'show'])->name('media.show');
Route::get('/media/{media}/download', [MediaController::class, 'download'])->name('media.download');
Route::get('/media/{media}/stream', [MediaController::class, 'stream'])->name('media.stream');

// No public dashboard - users start from home/welcome page


// Favorites functionality - session-based for all users
Route::get('/favorites', [App\Http\Controllers\FavoritesController::class, 'index'])->name('favorites.index');
Route::post('/quotes/{quote}/favorite', [QuoteController::class, 'toggleFavorite'])->name('quotes.favorite');
Route::post('/favorites/quote/{quote}/toggle', [FavoriteController::class, 'toggle'])
    ->name('favorites.toggle');

// Download routes
Route::get('/download/quote/{quote}/pdf', [App\Http\Controllers\DownloadController::class, 'quotePdf'])
    ->name('download.quote.pdf');
Route::get('/download/quote/{quote}/audio', [App\Http\Controllers\DownloadController::class, 'quoteAudio'])
    ->name('download.quote.audio');
Route::get('/stream/quote/{quote}/audio', [App\Http\Controllers\DownloadController::class, 'streamQuoteAudio'])
    ->name('stream.quote.audio');
Route::get('/stream/quote/{quote}/pdf', [App\Http\Controllers\DownloadController::class, 'streamQuotePdf'])
    ->name('stream.quote.pdf');

// About page - accessible to everyone
Route::get('/about', [App\Http\Controllers\AboutController::class, 'index'])->name('about');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
require __DIR__ . '/admin.php';

// API Routes for AJAX requests
Route::get('/api/quotes/random', function () {
    $quote = App\Models\Quote::with(['category', 'creator'])
        ->inRandomOrder()
        ->first();

    if (!$quote) {
        return response()->json(['error' => 'No quotes found'], 404);
    }

    return response()->json([
        'quote' => $quote
    ]);
});
