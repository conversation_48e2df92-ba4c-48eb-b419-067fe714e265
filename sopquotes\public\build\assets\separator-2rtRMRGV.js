import{r as p,j as e}from"./app-BWHLaRS2.js";import{P as d}from"./index-C3-VNe4Y.js";import{c as v}from"./button-CUovRkQ3.js";var m="Separator",n="horizontal",f=["horizontal","vertical"],s=p.forwardRef((r,t)=>{const{decorative:o,orientation:a=n,...l}=r,i=u(a)?a:n,c=o?{role:"none"}:{"aria-orientation":i==="vertical"?i:void 0,role:"separator"};return e.jsx(d.div,{"data-orientation":i,...c,...l,ref:t})});s.displayName=m;function u(r){return f.includes(r)}var h=s;function z({className:r,orientation:t="horizontal",decorative:o=!0,...a}){return e.jsx(h,{"data-slot":"separator-root",decorative:o,orientation:t,className:v("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",r),...a})}export{z as S};
