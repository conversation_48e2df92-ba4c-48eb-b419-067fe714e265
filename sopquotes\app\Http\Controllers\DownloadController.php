<?php

namespace App\Http\Controllers;

use App\Models\Quote;
use App\Models\UserDownload;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;

class DownloadController extends Controller
{
    /**
     * Download a quote's PDF file.
     */
    public function quotePdf(Quote $quote)
    {
        if (!$quote->pdf_path || !Storage::disk('public')->exists($quote->pdf_path)) {
            abort(404, 'PDF file not found.');
        }

        // Track the download
        $this->trackDownload($quote, 'pdf');

        $filePath = Storage::disk('public')->path($quote->pdf_path);
        $fileName = $this->generateFileName($quote, 'pdf');

        return response()->download($filePath, $fileName);
    }

    /**
     * Download a quote's audio file.
     */
    public function quoteAudio(Quote $quote)
    {
        if (!$quote->audio_path || !Storage::disk('public')->exists($quote->audio_path)) {
            abort(404, 'Audio file not found.');
        }

        // Track the download
        $this->trackDownload($quote, 'audio');

        $filePath = Storage::disk('public')->path($quote->audio_path);
        $fileName = $this->generateFileName($quote, 'audio');

        return response()->download($filePath, $fileName);
    }

    /**
     * Stream a quote's audio file for inline playback.
     */
    public function streamQuoteAudio(Quote $quote)
    {
        if (!$quote->audio_path || !Storage::disk('public')->exists($quote->audio_path)) {
            abort(404, 'Audio file not found.');
        }

        $filePath = Storage::disk('public')->path($quote->audio_path);
        $mimeType = $this->getAudioMimeType($quote->audio_path);

        return response()->file($filePath, [
            'Content-Type' => $mimeType,
            'Accept-Ranges' => 'bytes',
        ]);
    }

    /**
     * Stream a quote's PDF file for inline viewing.
     */
    public function streamQuotePdf(Quote $quote)
    {
        if (!$quote->pdf_path || !Storage::disk('public')->exists($quote->pdf_path)) {
            abort(404, 'PDF file not found.');
        }

        $filePath = Storage::disk('public')->path($quote->pdf_path);

        return response()->file($filePath, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="quote-' . $quote->id . '.pdf"',
        ]);
    }

    /**
     * Track a download in the database.
     */
    private function trackDownload($downloadable, $fileType)
    {
        // Only track downloads for authenticated users
        // Anonymous users can still download, but we don't track them for privacy
        if (auth()->check()) {
            UserDownload::create([
                'user_id' => auth()->id(),
                'downloadable_type' => get_class($downloadable),
                'downloadable_id' => $downloadable->id,
                'file_type' => $fileType,
                'downloaded_at' => now(),
            ]);
        }
    }

    /**
     * Generate a user-friendly filename for downloads.
     */
    private function generateFileName($quote, $fileType)
    {
        $extension = $fileType === 'pdf' ? 'pdf' : $this->getAudioExtension($quote->audio_path);

        // Create a clean filename from quote text
        $quoteText = substr($quote->text, 0, 50);
        $cleanText = preg_replace('/[^a-zA-Z0-9\s]/', '', $quoteText);
        $cleanText = preg_replace('/\s+/', '-', trim($cleanText));
        $cleanText = strtolower($cleanText);

        // Add quote ID for uniqueness
        return "sop-quote-{$quote->id}-{$cleanText}.{$extension}";
    }

    /**
     * Get the audio file extension.
     */
    private function getAudioExtension($filePath)
    {
        return pathinfo($filePath, PATHINFO_EXTENSION);
    }

    /**
     * Get the MIME type for audio files.
     */
    private function getAudioMimeType($filePath)
    {
        $extension = $this->getAudioExtension($filePath);

        return match ($extension) {
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'm4a' => 'audio/mp4',
            default => 'audio/mpeg',
        };
    }
}
