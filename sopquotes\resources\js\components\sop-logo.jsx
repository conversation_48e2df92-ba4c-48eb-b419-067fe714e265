import { BookOpen } from 'lucide-react';

export default function SOPLogo({ size = 'md', className = '', showGlow = false }) {
    const sizeClasses = {
        sm: 'h-8 w-8',
        md: 'h-16 w-16',
        lg: 'h-20 w-20',
        xl: 'h-24 w-24',
    };

    const iconSizes = {
        sm: 'h-4 w-4',
        md: 'h-8 w-8',
        lg: 'h-10 w-10',
        xl: 'h-12 w-12',
    };

    const textSizes = {
        sm: 'text-xs',
        md: 'text-sm',
        lg: 'text-base',
        xl: 'text-lg',
    };

    return (
        <div className={`relative ${className}`}>
            {/* Glow effect */}
            {showGlow && (
                <div className="absolute inset-0 bg-blue-600 rounded-full blur-lg opacity-20 group-hover:opacity-30 transition-opacity"></div>
            )}

            {/* Main logo circle */}
            <div className={`relative flex flex-col items-center justify-center ${sizeClasses[size]} rounded-full bg-gradient-to-br from-blue-600 to-indigo-600 shadow-lg`}>
                {/* Bible icon */}
                <BookOpen className={`${iconSizes[size]} text-white mb-0.5`} />

                {/* SOP text */}
                <span className={`${textSizes[size]} font-bold text-white leading-none`}>
                    SOPQ
                </span>
            </div>
        </div>
    );
}
