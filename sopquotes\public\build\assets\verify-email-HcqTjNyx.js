import{m as a,j as e,L as n}from"./app-BWHLaRS2.js";import{T as m}from"./text-link-BJC5FN6k.js";import{B as l}from"./button-CUovRkQ3.js";import{A as c}from"./auth-layout-BCFIpUOr.js";import{L as d}from"./loader-circle-Bb-8g7No.js";import"./card-3ac6awgC.js";import"./book-open-DxiZ0b6m.js";function y({status:i}){const{post:s,processing:t}=a({}),o=r=>{r.preventDefault(),s(route("verification.send"))};return e.jsxs(c,{title:"Verify email",description:"Please verify your email address by clicking on the link we just emailed to you.",children:[e.jsx(n,{title:"Email verification"}),i==="verification-link-sent"&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:"A new verification link has been sent to the email address you provided during registration."}),e.jsxs("form",{onSubmit:o,className:"space-y-6 text-center",children:[e.jsxs(l,{disabled:t,variant:"secondary",children:[t&&e.jsx(d,{className:"h-4 w-4 animate-spin"}),"Resend verification email"]}),e.jsx(m,{href:route("logout"),method:"post",className:"mx-auto block text-sm",children:"Log out"})]})]})}export{y as default};
