<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user first
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);

        // Create regular test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        // Seed categories and quotes from JSON files
        $this->call([
            QuotesFromJsonSeeder::class,
        ]);
    }
}
