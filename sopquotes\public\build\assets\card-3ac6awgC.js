import{j as t}from"./app-BWHLaRS2.js";import{c as e}from"./button-CUovRkQ3.js";function o({className:a,...r}){return t.jsx("div",{"data-slot":"card",className:e("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function n({className:a,...r}){return t.jsx("div",{"data-slot":"card-header",className:e("flex flex-col gap-1.5 px-6",a),...r})}function c({className:a,...r}){return t.jsx("div",{"data-slot":"card-title",className:e("leading-none font-semibold",a),...r})}function l({className:a,...r}){return t.jsx("div",{"data-slot":"card-content",className:e("px-6",a),...r})}export{o as C,n as a,c as b,l as c};
