import{r as x,m as D,j as e,L as U,$ as F}from"./app-BWHLaRS2.js";import{A as L}from"./admin-layout-DKXm94gZ.js";import{B as o}from"./button-CUovRkQ3.js";import{C as m,a as h,b as j,c as p}from"./card-3ac6awgC.js";import{I as f}from"./input-B4rX1XfI.js";import{L as r}from"./label-DWihlnjb.js";import{T as P,S as z}from"./textarea-DsrDZwdm.js";import{C as _}from"./checkbox-B6mGW3Gq.js";import{B as E}from"./badge-BSmw6aR2.js";import{A as G}from"./arrow-left-B-2ALS0F.js";import{P as I}from"./plus-CzNwqLHA.js";import{X as $}from"./flash-message-BOB8jroJ.js";import"./sun-Bmefm2yw.js";import"./book-open-DxiZ0b6m.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./index-BDjL_iy4.js";function oe({categories:u=[],auth:K}){const[l,g]=x.useState([]),[n,y]=x.useState(""),[d,b]=x.useState(null),{data:i,setData:c,post:w,processing:v,errors:t}=D({title:"",description:"",category_id:"",tags:[],is_featured:!1,is_active:!0,file:null}),M=s=>{s.preventDefault();const a=new FormData;a.append("title",i.title),a.append("description",i.description),a.append("category_id",i.category_id),a.append("tags",JSON.stringify(l)),a.append("is_featured",i.is_featured?"1":"0"),a.append("is_active",i.is_active?"1":"0"),d&&a.append("file",d),w("/admin/media",{data:a,forceFormData:!0})},N=()=>{n.trim()&&!l.includes(n.trim())&&(g([...l,n.trim()]),y(""))},S=s=>{g(l.filter(a=>a!==s))},k=s=>{const a=s.target.files[0];b(a),c("file",a)},A=s=>s?s.type.startsWith("audio/")?"audio":s.type==="application/pdf"?"pdf":"other":"",B=s=>{if(s===0)return"0 Bytes";const a=1024,T=["Bytes","KB","MB","GB"],C=Math.floor(Math.log(s)/Math.log(a));return parseFloat((s/Math.pow(a,C)).toFixed(2))+" "+T[C]};return e.jsxs(L,{children:[e.jsx(U,{title:"Upload Media - Admin Dashboard"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(F,{href:"/admin/media",children:e.jsxs(o,{variant:"outline",size:"sm",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Back to Media"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Upload Media"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Upload PDF documents and audio files"})]})]})}),e.jsx("form",{onSubmit:M,className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(m,{children:[e.jsx(h,{children:e.jsx(j,{children:"Basic Information"})}),e.jsxs(p,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"title",children:"Title *"}),e.jsx(f,{id:"title",type:"text",value:i.title,onChange:s=>c("title",s.target.value),className:t.title?"border-red-500":"",placeholder:"Enter media title..."}),t.title&&e.jsx("p",{className:"text-red-500 text-sm",children:t.title})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"description",children:"Description"}),e.jsx(P,{id:"description",value:i.description,onChange:s=>c("description",s.target.value),className:t.description?"border-red-500":"",placeholder:"Enter media description...",rows:4}),t.description&&e.jsx("p",{className:"text-red-500 text-sm",children:t.description})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"category",children:"Category"}),e.jsxs("select",{id:"category",value:i.category_id,onChange:s=>c("category_id",s.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${t.category_id?"border-red-500":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select a category"}),Array.isArray(u)&&u.map(s=>e.jsx("option",{value:s.id.toString(),children:s.name},s.id))]}),t.category_id&&e.jsx("p",{className:"text-red-500 text-sm",children:t.category_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"tags",children:"Tags"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(f,{type:"text",value:n,onChange:s=>y(s.target.value),placeholder:"Add a tag...",onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),N())}}),e.jsx(o,{type:"button",onClick:N,variant:"outline",children:e.jsx(I,{className:"h-4 w-4"})})]}),l.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:l.map((s,a)=>e.jsxs(E,{variant:"secondary",className:"flex items-center space-x-1",children:[e.jsx("span",{children:s}),e.jsx("button",{type:"button",onClick:()=>S(s),className:"ml-1 hover:text-red-500",children:e.jsx($,{className:"h-3 w-3"})})]},a))})]})]})]}),e.jsxs(m,{children:[e.jsx(h,{children:e.jsx(j,{children:"File Upload"})}),e.jsxs(p,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"file",children:"Media File *"}),e.jsx(f,{id:"file",type:"file",accept:".pdf,.mp3,.wav,.ogg,.m4a",onChange:k,className:t.file?"border-red-500":""}),e.jsx("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, MP3, WAV, OGG, M4A (Max: 50MB)"}),t.file&&e.jsx("p",{className:"text-red-500 text-sm",children:t.file})]}),d&&e.jsxs("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Selected File:"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Name:"})," ",d.name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Size:"})," ",B(d.size)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Type:"})," ",A(d).toUpperCase()]})]})]})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(m,{children:[e.jsx(h,{children:e.jsx(j,{children:"Settings"})}),e.jsxs(p,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{id:"is_featured",checked:i.is_featured,onCheckedChange:s=>c("is_featured",s)}),e.jsx(r,{htmlFor:"is_featured",children:"Featured Media"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{id:"is_active",checked:i.is_active,onCheckedChange:s=>c("is_active",s)}),e.jsx(r,{htmlFor:"is_active",children:"Active"})]})]})]}),e.jsx(m,{children:e.jsx(p,{className:"pt-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(o,{type:"submit",disabled:v,className:"w-full",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),v?"Uploading...":"Upload Media"]}),e.jsx(F,{href:"/admin/media",children:e.jsx(o,{variant:"outline",className:"w-full",children:"Cancel"})})]})})})]})]})})]})]})}export{oe as default};
