import{r as x,j as e,L,$ as u,S as n}from"./app-BWHLaRS2.js";import{A as V}from"./admin-layout-DKXm94gZ.js";import{B as t}from"./button-CUovRkQ3.js";import{C as a,a as r,b as l,c as i}from"./card-3ac6awgC.js";import{B as h}from"./badge-BSmw6aR2.js";import{I as q}from"./input-B4rX1XfI.js";import{U as j}from"./upload-Cb6Ub9xq.js";import{F as m,V as D}from"./volume-2-DIbtoXia.js";import{D as G}from"./download-Bv0PHygq.js";import{S as P}from"./search-H5umLGXz.js";import{E as _}from"./eye-CbhFpv9_.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./book-open-DxiZ0b6m.js";import"./sun-Bmefm2yw.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";function ce({media:d={data:[],total:0,links:[]},categories:p=[],filters:f={},auth:H={}}){var A;const c=Array.isArray(f)?{}:f,[g,v]=x.useState(String(c.search||"")),[y,N]=x.useState(String(c.file_type||"")),[w,b]=x.useState(String(c.category||"")),[F,S]=x.useState(String(c.status||"")),k=s=>{s.preventDefault(),n.get("/admin/media",{search:g,file_type:y,category:w,status:F},{preserveState:!0,preserveScroll:!0})},M=()=>{v(""),N(""),b(""),S(""),n.get("/admin/media",{},{preserveState:!0,preserveScroll:!0})},T=s=>{confirm(`Are you sure you want to delete "${s.title}"? This action cannot be undone.`)&&n.delete(`/admin/media/${s.id}`,{preserveScroll:!0})},z=s=>{n.patch(`/admin/media/${s.id}/toggle-featured`,{},{preserveScroll:!0})},B=s=>{n.patch(`/admin/media/${s.id}/toggle-status`,{},{preserveScroll:!0})},U=s=>{if(s===0)return"0 Bytes";const o=1024,E=["Bytes","KB","MB","GB"],C=Math.floor(Math.log(s)/Math.log(o));return parseFloat((s/Math.pow(o,C)).toFixed(2))+" "+E[C]},$=s=>{switch(s){case"pdf":return e.jsx(m,{className:"h-8 w-8 text-red-500"});case"audio":return e.jsx(D,{className:"h-8 w-8 text-blue-500"});default:return e.jsx(m,{className:"h-8 w-8 text-gray-500"})}},I=s=>new Date(s).toLocaleDateString();return e.jsxs(V,{children:[e.jsx(L,{title:"Media Gallery - Admin Dashboard"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Media Gallery"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Manage PDF and audio files attached to quotes"})]}),e.jsxs(t,{children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Upload Files"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Total Files"}),e.jsx(m,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:mediaFiles.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Across all quotes"})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"PDF Files"}),e.jsx(m,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:mediaFiles.filter(s=>s.type==="pdf").length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Document files"})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Audio Files"}),e.jsx(D,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:mediaFiles.filter(s=>s.type==="audio").length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Audio recordings"})]})]}),e.jsxs(a,{children:[e.jsxs(r,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(l,{className:"text-sm font-medium",children:"Total Downloads"}),e.jsx(G,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(i,{children:[e.jsx("div",{className:"text-2xl font-bold",children:mediaFiles.reduce((s,o)=>s+o.downloads,0)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"All time downloads"})]})]})]}),e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(l,{children:"Filters"})}),e.jsx(i,{children:e.jsx("form",{onSubmit:k,className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[e.jsx("div",{children:e.jsx(q,{type:"text",placeholder:"Search files...",value:g,onChange:s=>v(s.target.value)})}),e.jsx("div",{children:e.jsxs("select",{value:y,onChange:s=>N(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All File Types"}),e.jsx("option",{value:"pdf",children:"PDF Files"}),e.jsx("option",{value:"audio",children:"Audio Files"})]})}),e.jsx("div",{children:e.jsxs("select",{value:w,onChange:s=>b(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Categories"}),Array.isArray(p)&&p.map(s=>e.jsx("option",{value:s.id.toString(),children:s.name},s.id))]})}),e.jsx("div",{children:e.jsxs("select",{value:F,onChange:s=>S(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(t,{type:"submit",children:[e.jsx(P,{className:"h-4 w-4 mr-2"}),"Search"]}),e.jsx(t,{type:"button",variant:"outline",onClick:M,children:"Clear"})]})]})})})]}),e.jsxs(a,{children:[e.jsx(r,{children:e.jsx(l,{children:"Media Files"})}),e.jsx(i,{children:e.jsx("div",{className:"space-y-4",children:((A=d==null?void 0:d.data)==null?void 0:A.length)>0?d.data.map(s=>e.jsx("div",{className:"border rounded-lg p-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0",children:$(s.file_type)}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s.title}),e.jsx(h,{variant:s.file_type==="pdf"?"destructive":"default",children:s.file_type.toUpperCase()}),s.is_featured&&e.jsx(h,{variant:"secondary",children:"Featured"}),e.jsx(h,{variant:s.is_active?"default":"outline",children:s.is_active?"Active":"Inactive"})]}),s.description&&e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-2 line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("span",{children:["Size: ",U(s.file_size)]}),e.jsxs("span",{children:["Views: ",s.view_count||0]}),e.jsxs("span",{children:["Uploaded: ",I(s.created_at)]}),s.category&&e.jsxs("span",{children:["Category: ",s.category.name]})]})]})]}),e.jsxs("div",{className:"flex flex-col space-y-2 ml-4",children:[e.jsx(u,{href:`/admin/media/${s.id}`,children:e.jsxs(t,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"View"]})}),e.jsx(u,{href:`/admin/media/${s.id}/edit`,children:e.jsxs(t,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Edit"]})}),e.jsx(t,{variant:"outline",size:"sm",className:"w-full",onClick:()=>z(s),children:s.is_featured?"Unfeature":"Feature"}),e.jsx(t,{variant:"outline",size:"sm",className:"w-full",onClick:()=>B(s),children:s.is_active?"Deactivate":"Activate"}),e.jsx(t,{variant:"destructive",size:"sm",className:"w-full",onClick:()=>T(s),children:"Delete"})]})]})},s.id)):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(j,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500 text-lg",children:"No media files found"}),e.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"Upload files through the quote management interface or create new media files"}),e.jsx(u,{href:"/admin/media/create",children:e.jsxs(t,{className:"mt-4",children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Upload Media"]})})]})})})]})]})]})}export{ce as default};
