import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Search, Download, FileText, Volume2, Play, Eye, Filter } from 'lucide-react';
import QuotesLayout from '@/layouts/quotes-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';


export default function MediaIndex({ media = { data: [], total: 0, links: [] }, categories = [], filters = {} }) {
    // Ensure filters is an object, not an array
    const safeFilters = Array.isArray(filters) ? {} : filters;

    const [searchTerm, setSearchTerm] = useState(String(safeFilters.search || ''));
    const [fileTypeFilter, setFileTypeFilter] = useState(String(safeFilters.file_type || ''));
    const [categoryFilter, setCategoryFilter] = useState(String(safeFilters.category || ''));



    const handleSearch = (e) => {
        e.preventDefault();
        router.get('/media', {
            search: searchTerm,
            file_type: fileTypeFilter,
            category: categoryFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setFileTypeFilter('');
        setCategoryFilter('');
        router.get('/media', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getFileIcon = (fileType) => {
        switch (fileType) {
            case 'audio':
                return <Volume2 className="h-8 w-8 text-blue-500" />;
            case 'pdf':
                return <FileText className="h-8 w-8 text-red-500" />;
            default:
                return <FileText className="h-8 w-8 text-gray-500" />;
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (date) => {
        return new Date(date).toLocaleDateString();
    };

    return (
        <QuotesLayout>
            <Head title="Media Library - SOP Quotes" />

            <div className="space-y-6">
                {/* Header */}
                <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Media Library</h1>
                    <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                        Discover and download inspiring Christian content including PDF documents and audio recordings
                    </p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center">
                                <FileText className="h-8 w-8 text-red-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">PDF Documents</p>
                                    <p className="text-2xl font-bold">
                                        {media?.data?.filter(item => item.file_type === 'pdf').length || 0}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center">
                                <Volume2 className="h-8 w-8 text-blue-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">Audio Files</p>
                                    <p className="text-2xl font-bold">
                                        {media?.data?.filter(item => item.file_type === 'audio').length || 0}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center">
                                <Download className="h-8 w-8 text-green-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-muted-foreground">Total Files</p>
                                    <p className="text-2xl font-bold">{media?.total || 0}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="h-5 w-5 mr-2" />
                            Search & Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <Input
                                        type="text"
                                        placeholder="Search media..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <select
                                        value={fileTypeFilter}
                                        onChange={(e) => setFileTypeFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All File Types</option>
                                        <option value="pdf">PDF Documents</option>
                                        <option value="audio">Audio Files</option>
                                    </select>
                                </div>

                                <div>
                                    <select
                                        value={categoryFilter}
                                        onChange={(e) => setCategoryFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Categories</option>
                                        {Array.isArray(categories) && categories.map(category => (
                                            <option key={category.id} value={category.id.toString()}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="flex space-x-2">
                                    <Button type="submit">
                                        <Search className="h-4 w-4 mr-2" />
                                        Search
                                    </Button>
                                    <Button type="button" variant="outline" onClick={clearFilters}>
                                        Clear
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Media Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {media?.data?.length > 0 ? media.data.map((file) => (
                        <Card key={file.id} className="hover:shadow-lg transition-shadow">
                            <CardContent className="p-6">
                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0">
                                        {getFileIcon(file.file_type)}
                                    </div>

                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2 mb-2">
                                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                                {file.title}
                                            </h3>
                                            <Badge variant={file.file_type === 'pdf' ? 'destructive' : 'default'}>
                                                {file.file_type.toUpperCase()}
                                            </Badge>
                                            {file.is_featured && (
                                                <Badge variant="secondary">Featured</Badge>
                                            )}
                                        </div>

                                        {file.description && (
                                            <p className="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                                                {file.description}
                                            </p>
                                        )}

                                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                                            <span>Size: {formatFileSize(file.file_size)}</span>
                                            <span>Views: {file.view_count || 0}</span>
                                            {file.category && (
                                                <span>Category: {file.category.name}</span>
                                            )}
                                        </div>

                                        <div className="flex space-x-2">
                                            <Link href={`/media/${file.id}`}>
                                                <Button variant="outline" size="sm" className="flex-1">
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    View
                                                </Button>
                                            </Link>

                                            <Link href={`/media/${file.id}/download`}>
                                                <Button size="sm" className="flex-1">
                                                    <Download className="h-4 w-4 mr-2" />
                                                    Download
                                                </Button>
                                            </Link>

                                            {file.file_type === 'audio' && (
                                                <Link href={`/media/${file.id}/stream`}>
                                                    <Button variant="outline" size="sm">
                                                        <Play className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )) : (
                        <div className="col-span-full text-center py-12">
                            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500 text-lg">No media files found</p>
                            <p className="text-gray-400 text-sm mt-2">
                                Try adjusting your search criteria or check back later for new content
                            </p>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {media?.links && media.links.length > 3 && (
                    <div className="flex justify-center">
                        <div className="flex space-x-1">
                            {media.links.map((link, index) => (
                                <Link
                                    key={index}
                                    href={link.url || '#'}
                                    className={`px-3 py-2 text-sm rounded-md ${link.active
                                        ? 'bg-blue-500 text-white'
                                        : link.url
                                            ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                        }`}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </QuotesLayout>
    );
}
