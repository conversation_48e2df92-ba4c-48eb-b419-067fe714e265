import{r as n,j as p}from"./app-BWHLaRS2.js";import{r as f}from"./index-CB7we0-r.js";import{S as u}from"./button-CUovRkQ3.js";var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],x=d.reduce((i,r)=>{const t=n.forwardRef((o,s)=>{const{asChild:e,...a}=o,m=e?u:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),p.jsx(m,{...a,ref:s})});return t.displayName=`Primitive.${r}`,{...i,[r]:t}},{});function h(i,r){i&&f.flushSync(()=>i.dispatchEvent(r))}export{x as P,h as d};
