import{m as N,j as e,L as b,$ as h,S as C}from"./app-BWHLaRS2.js";import{A as y}from"./admin-layout-DKXm94gZ.js";import{B as r}from"./button-CUovRkQ3.js";import{C as d,a as l,b as c,c as o}from"./card-3ac6awgC.js";import{I as k}from"./input-B4rX1XfI.js";import{L as m}from"./label-DWihlnjb.js";import{T as q,S as p}from"./textarea-DsrDZwdm.js";import{C as w}from"./checkbox-B6mGW3Gq.js";import{B as A}from"./badge-BSmw6aR2.js";import{A as T}from"./arrow-left-B-2ALS0F.js";import{T as L}from"./trash-2-DpyI9JiF.js";import{B as u}from"./book-open-DxiZ0b6m.js";import{T as S}from"./tag-CF10xzlC.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./sun-Bmefm2yw.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";import"./index-C3-VNe4Y.js";import"./index-BDjL_iy4.js";function ee({category:s,auth:D}){const{data:i,setData:n,put:j,processing:x,errors:a,reset:g}=N({name:s.name||"",description:s.description||"",is_active:s.is_active||!1}),v=t=>{t.preventDefault(),j(route("admin.categories.update",s.id))},f=()=>{if(s.quotes&&s.quotes.length>0){alert(`Cannot delete "${s.name}" because it has ${s.quotes.length} quotes. Please move or delete the quotes first.`);return}confirm(`Are you sure you want to delete the category "${s.name}"? This action cannot be undone.`)&&C.delete(route("admin.categories.destroy",s.id))};return e.jsxs(y,{children:[e.jsx(b,{title:`Edit ${s.name} - Admin Dashboard`}),e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Edit Category"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Update category information and settings"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(h,{href:"/admin/categories",children:e.jsxs(r,{variant:"outline",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Back to Categories"]})}),e.jsxs(r,{variant:"destructive",onClick:f,children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Delete Category"]})]})]}),e.jsxs(d,{children:[e.jsx(l,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(u,{className:"h-5 w-5"}),e.jsx("span",{children:"Category Statistics"})]})}),e.jsxs(o,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:s.quotes?s.quotes.length:0}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Quotes"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:s.is_active?"Active":"Inactive"}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Status"})]}),e.jsxs("div",{className:"text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:s.slug}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"URL Slug"})]})]}),s.quotes&&s.quotes.length>0&&e.jsx("div",{className:"mt-4",children:e.jsx(h,{href:`/quotes?category=${s.slug}`,children:e.jsxs(r,{variant:"outline",size:"sm",children:[e.jsx(u,{className:"h-4 w-4 mr-2"}),"View All Quotes in This Category"]})})})]})]}),e.jsxs(d,{children:[e.jsx(l,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(S,{className:"h-5 w-5"}),e.jsx("span",{children:"Category Information"})]})}),e.jsx(o,{children:e.jsxs("form",{onSubmit:v,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"name",children:"Category Name *"}),e.jsx(k,{id:"name",value:i.name,onChange:t=>n("name",t.target.value),placeholder:"e.g., Faith, Hope, Love",className:a.name?"border-red-500":""}),a.name&&e.jsx("p",{className:"text-red-500 text-sm",children:a.name}),e.jsx("p",{className:"text-sm text-gray-500",children:"Changing the name will update the URL slug automatically."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"description",children:"Description"}),e.jsx(q,{id:"description",value:i.description,onChange:t=>n("description",t.target.value),placeholder:"Describe what types of quotes belong in this category...",rows:3,className:a.description?"border-red-500":""}),a.description&&e.jsx("p",{className:"text-red-500 text-sm",children:a.description})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(w,{id:"is_active",checked:i.is_active,onCheckedChange:t=>n("is_active",t)}),e.jsx(m,{htmlFor:"is_active",children:"Active Category"})]}),e.jsx("p",{className:"text-sm text-gray-500 ml-6",children:"Inactive categories are hidden from users and cannot be assigned to new quotes."}),e.jsxs("div",{className:"flex justify-end space-x-4 pt-6",children:[e.jsx(r,{type:"button",variant:"outline",onClick:()=>g(),children:"Reset Changes"}),e.jsx(r,{type:"submit",disabled:x,children:x?e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"h-4 w-4 mr-2 animate-spin"}),"Updating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"h-4 w-4 mr-2"}),"Update Category"]})})]})]})})]}),s.quotes&&s.quotes.length>0&&e.jsxs(d,{className:"border-red-200 dark:border-red-800",children:[e.jsx(l,{children:e.jsx(c,{className:"text-red-600 dark:text-red-400",children:"Danger Zone"})}),e.jsx(o,{children:e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg",children:[e.jsxs("p",{className:"text-sm text-red-800 dark:text-red-200 mb-3",children:["This category cannot be deleted because it contains ",s.quotes.length," quotes. You must first move or delete all quotes in this category before it can be deleted."]}),e.jsxs(A,{variant:"destructive",children:[s.quotes.length," quotes must be handled first"]})]})})]})]})]})}export{ee as default};
