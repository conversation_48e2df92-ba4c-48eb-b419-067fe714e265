<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class UserDownload extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'downloadable_type',
        'downloadable_id',
        'file_type',
        'downloaded_at',
    ];

    protected $casts = [
        'downloaded_at' => 'datetime',
    ];

    /**
     * Get the user that owns the download.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the downloadable model (Book, Media).
     */
    public function downloadable(): MorphTo
    {
        return $this->morphTo();
    }
}
