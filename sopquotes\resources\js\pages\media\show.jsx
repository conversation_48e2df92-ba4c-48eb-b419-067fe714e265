import { Head, <PERSON> } from '@inertiajs/react';
import { useState, useRef } from 'react';
import { ArrowLeft, Download, Play, Pause, Volume2, FileText, Eye, Calendar, Tag, User } from 'lucide-react';
import QuotesLayout from '@/layouts/quotes-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export default function MediaShow({ media, relatedMedia = [] }) {
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const audioRef = useRef(null);

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (time) => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    const togglePlayPause = () => {
        if (audioRef.current) {
            if (isPlaying) {
                audioRef.current.pause();
            } else {
                audioRef.current.play();
            }
            setIsPlaying(!isPlaying);
        }
    };

    const handleTimeUpdate = () => {
        if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime);
        }
    };

    const handleLoadedMetadata = () => {
        if (audioRef.current) {
            setDuration(audioRef.current.duration);
        }
    };

    const handleSeek = (e) => {
        if (audioRef.current) {
            const rect = e.currentTarget.getBoundingClientRect();
            const percent = (e.clientX - rect.left) / rect.width;
            const newTime = percent * duration;
            audioRef.current.currentTime = newTime;
            setCurrentTime(newTime);
        }
    };

    const getFileIcon = (fileType) => {
        switch (fileType) {
            case 'audio':
                return <Volume2 className="h-12 w-12 text-blue-500" />;
            case 'pdf':
                return <FileText className="h-12 w-12 text-red-500" />;
            default:
                return <FileText className="h-12 w-12 text-gray-500" />;
        }
    };

    return (
        <QuotesLayout>
            <Head title={`${media.title} - Media Library`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center space-x-4">
                    <Link href="/media">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Media
                        </Button>
                    </Link>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Media Player/Viewer */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="text-center">
                                    <div className="flex justify-center mb-6">
                                        {getFileIcon(media.file_type)}
                                    </div>

                                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                        {media.title}
                                    </h1>

                                    <div className="flex justify-center space-x-2 mb-6">
                                        <Badge variant={media.file_type === 'pdf' ? 'destructive' : 'default'}>
                                            {media.file_type.toUpperCase()}
                                        </Badge>
                                        {media.is_featured && (
                                            <Badge variant="secondary">Featured</Badge>
                                        )}
                                        {media.category && (
                                            <Badge variant="outline">{media.category.name}</Badge>
                                        )}
                                    </div>

                                    {/* Audio Player */}
                                    {media.file_type === 'audio' && (
                                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
                                            <audio
                                                ref={audioRef}
                                                src={`/media/${media.id}/stream`}
                                                onTimeUpdate={handleTimeUpdate}
                                                onLoadedMetadata={handleLoadedMetadata}
                                                onEnded={() => setIsPlaying(false)}
                                                preload="metadata"
                                            />

                                            <div className="space-y-4">
                                                <Button
                                                    onClick={togglePlayPause}
                                                    size="lg"
                                                    className="w-full"
                                                >
                                                    {isPlaying ? (
                                                        <Pause className="h-6 w-6 mr-2" />
                                                    ) : (
                                                        <Play className="h-6 w-6 mr-2" />
                                                    )}
                                                    {isPlaying ? 'Pause' : 'Play'}
                                                </Button>

                                                {/* Progress Bar */}
                                                <div className="space-y-2">
                                                    <div
                                                        className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 cursor-pointer"
                                                        onClick={handleSeek}
                                                    >
                                                        <div
                                                            className="bg-blue-500 h-2 rounded-full transition-all duration-100"
                                                            style={{
                                                                width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%'
                                                            }}
                                                        />
                                                    </div>
                                                    <div className="flex justify-between text-sm text-gray-500">
                                                        <span>{formatTime(currentTime)}</span>
                                                        <span>{formatTime(duration)}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* PDF Viewer */}
                                    {media.file_type === 'pdf' && (
                                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
                                            <iframe
                                                src={`/media/${media.id}/stream`}
                                                className="w-full h-96 border rounded"
                                                title={media.title}
                                            />
                                            <p className="text-sm text-gray-500 mt-2">
                                                PDF preview - Download for full functionality
                                            </p>
                                        </div>
                                    )}

                                    {/* Download Button */}
                                    <Link href={`/media/${media.id}/download`}>
                                        <Button size="lg" className="w-full">
                                            <Download className="h-5 w-5 mr-2" />
                                            Download {media.file_type.toUpperCase()}
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Description */}
                        {media.description && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Description</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                                        {media.description}
                                    </p>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* File Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>File Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-3">
                                    <FileText className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <p className="text-sm font-medium">File Size</p>
                                        <p className="text-sm text-gray-600">{formatFileSize(media.file_size)}</p>
                                    </div>
                                </div>

                                <Separator />

                                <div className="flex items-center space-x-3">
                                    <Calendar className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <p className="text-sm font-medium">Upload Date</p>
                                        <p className="text-sm text-gray-600">{formatDate(media.created_at)}</p>
                                    </div>
                                </div>

                                <Separator />

                                <div className="flex items-center space-x-3">
                                    <Eye className="h-5 w-5 text-gray-500" />
                                    <div>
                                        <p className="text-sm font-medium">Views</p>
                                        <p className="text-sm text-gray-600">{media.view_count || 0}</p>
                                    </div>
                                </div>

                                {media.category && (
                                    <>
                                        <Separator />
                                        <div className="flex items-center space-x-3">
                                            <Tag className="h-5 w-5 text-gray-500" />
                                            <div>
                                                <p className="text-sm font-medium">Category</p>
                                                <p className="text-sm text-gray-600">{media.category.name}</p>
                                            </div>
                                        </div>
                                    </>
                                )}

                                {media.tags && media.tags.length > 0 && (
                                    <>
                                        <Separator />
                                        <div>
                                            <p className="text-sm font-medium mb-2">Tags</p>
                                            <div className="flex flex-wrap gap-1">
                                                {media.tags.map((tag, index) => (
                                                    <Badge key={index} variant="outline" className="text-xs">
                                                        {tag}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </CardContent>
                        </Card>

                        {/* Related Media */}
                        {relatedMedia.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Related Media</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {relatedMedia.slice(0, 3).map((item) => (
                                        <Link key={item.id} href={`/media/${item.id}`}>
                                            <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                                <div className="flex-shrink-0">
                                                    {item.file_type === 'audio' ? (
                                                        <Volume2 className="h-6 w-6 text-blue-500" />
                                                    ) : (
                                                        <FileText className="h-6 w-6 text-red-500" />
                                                    )}
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                        {item.title}
                                                    </p>
                                                    <p className="text-xs text-gray-500">
                                                        {item.file_type.toUpperCase()} • {formatFileSize(item.file_size)}
                                                    </p>
                                                </div>
                                            </div>
                                        </Link>
                                    ))}
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </QuotesLayout>
    );
}
