import { Head, Link, router, useForm } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { ArrowLeft, Save, Plus, X } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

export default function AdminQuoteEdit({ quote = {}, categories = [] }) {
  const { data, setData, processing, put, errors, reset, wasSuccessful } = useForm({
    text: quote.text || '',
    source: quote.source || '',
    author: quote.author || '',
    reference: quote.reference || '',
    category_id: quote.category_id || '',
    // Controller expects JSON string for tags
    tags: JSON.stringify(Array.isArray(quote.tags) ? quote.tags : []),
    is_featured: !!quote.is_featured,

  });

  // Local state to manage tags as an array
  const [tags, setTags] = useState(Array.isArray(quote.tags) ? quote.tags : []);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    // Keep data.tags in sync as JSON string
    setData('tags', JSON.stringify(tags));
  }, [tags]);

  const addTag = (e) => {
    e.preventDefault();
    const t = newTag.trim();
    if (!t) return;
    if (tags.includes(t)) {
      setNewTag('');
      return;
    }
    setTags([...tags, t]);
    setNewTag('');
  };

  const removeTag = (t) => {
    setTags(tags.filter((x) => x !== t));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    put(`/admin/quotes/${quote.id}`, {
      preserveScroll: true,
    });
  };

  return (
    <AdminLayout>
      <Head title={`Edit Quote #${quote.id}`} />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>

            <p className="text-gray-600 dark:text-gray-400 mt-2">Update the quote details below</p>
          </div>
          <Link href="/admin/quotes">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quotes
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Quote Details</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="text">Text</Label>
                <TextareaLike
                  id="text"
                  value={data.text}
                  onChange={(e) => setData('text', e.target.value)}
                  placeholder="Enter the quote text"
                />
                {errors.text && <p className="text-sm text-red-600">{errors.text}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="author">Author</Label>
                  <Input id="author" value={data.author} onChange={(e) => setData('author', e.target.value)} />
                  {errors.author && <p className="text-sm text-red-600">{errors.author}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="source">Source</Label>
                  <Input id="source" value={data.source} onChange={(e) => setData('source', e.target.value)} />
                  {errors.source && <p className="text-sm text-red-600">{errors.source}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reference">Reference</Label>
                  <Input id="reference" value={data.reference} onChange={(e) => setData('reference', e.target.value)} />
                  {errors.reference && <p className="text-sm text-red-600">{errors.reference}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category_id">Category</Label>
                  <select
                    id="category_id"
                    value={data.category_id || ''}
                    onChange={(e) => setData('category_id', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">No category</option>
                    {categories?.map((c) => (
                      <option key={c.id} value={c.id}>
                        {c.name}
                      </option>
                    ))}
                  </select>
                  {errors.category_id && <p className="text-sm text-red-600">{errors.category_id}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="Add a tag and press +"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') addTag(e);
                    }}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tags.map((t) => (
                      <span key={t} className="inline-flex items-center gap-1 rounded-full bg-gray-200 px-2 py-1 text-xs text-gray-800 dark:bg-gray-700 dark:text-gray-100">
                        {t}
                        <button type="button" onClick={() => removeTag(t)} className="ml-1">
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 gap-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_featured"
                    checked={!!data.is_featured}
                    onCheckedChange={(v) => setData('is_featured', Boolean(v))}
                  />
                  <Label htmlFor="is_featured">Featured</Label>
                </div>
                {errors.is_featured && <p className="text-sm text-red-600">{errors.is_featured}</p>}


              </div>

              <div className="flex items-center justify-end gap-2">
                <Link href="/admin/quotes">
                  <Button type="button" variant="outline">Cancel</Button>
                </Link>
                <Button type="submit" disabled={processing}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

// Small textarea replacement to keep bundle simple
function TextareaLike({ value, onChange, id, placeholder }) {
  return (
    <textarea
      id={id}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className="min-h-[140px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-neutral-900 dark:text-white"
    />
  );
}

