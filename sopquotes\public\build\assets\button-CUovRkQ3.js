import{r as v,j as W}from"./app-BWHLaRS2.js";function de(e,o){if(typeof e=="function")return e(o);e!=null&&(e.current=o)}function me(...e){return o=>{let r=!1;const t=e.map(n=>{const l=de(n,o);return!r&&typeof l=="function"&&(r=!0),l});if(r)return()=>{for(let n=0;n<t.length;n++){const l=t[n];typeof l=="function"?l():de(e[n],null)}}}}function zr(...e){return v.useCallback(me(...e),e)}var he=v.forwardRef((e,o)=>{const{children:r,...t}=e,n=v.Children.toArray(r),l=n.find(Pe);if(l){const a=l.props.children,p=n.map(d=>d===l?v.Children.count(a)>1?v.Children.only(null):v.isValidElement(a)?a.props.children:null:d);return W.jsx(Q,{...t,ref:o,children:v.isValidElement(a)?v.cloneElement(a,void 0,p):null})}return W.jsx(Q,{...t,ref:o,children:r})});he.displayName="Slot";var Q=v.forwardRef((e,o)=>{const{children:r,...t}=e;if(v.isValidElement(r)){const n=Ee(r),l=Ie(t,r.props);return r.type!==v.Fragment&&(l.ref=o?me(o,n):n),v.cloneElement(r,l)}return v.Children.count(r)>1?v.Children.only(null):null});Q.displayName="SlotClone";var Re=({children:e})=>W.jsx(W.Fragment,{children:e});function Pe(e){return v.isValidElement(e)&&e.type===Re}function Ie(e,o){const r={...o};for(const t in o){const n=e[t],l=o[t];/^on[A-Z]/.test(t)?n&&l?r[t]=(...p)=>{l(...p),n(...p)}:n&&(r[t]=n):t==="style"?r[t]={...n,...l}:t==="className"&&(r[t]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}function Ee(e){var t,n;let o=(t=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:t.get,r=o&&"isReactWarning"in o&&o.isReactWarning;return r?e.ref:(o=(n=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:n.get,r=o&&"isReactWarning"in o&&o.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function ve(e){var o,r,t="";if(typeof e=="string"||typeof e=="number")t+=e;else if(typeof e=="object")if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(r=ve(e[o]))&&(t&&(t+=" "),t+=r)}else for(r in e)e[r]&&(t&&(t+=" "),t+=r);return t}function xe(){for(var e,o,r=0,t="",n=arguments.length;r<n;r++)(e=arguments[r])&&(o=ve(e))&&(t&&(t+=" "),t+=o);return t}const ue=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,pe=xe,Ge=(e,o)=>r=>{var t;if((o==null?void 0:o.variants)==null)return pe(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:n,defaultVariants:l}=o,a=Object.keys(n).map(f=>{const g=r==null?void 0:r[f],x=l==null?void 0:l[f];if(g===null)return null;const z=ue(g)||ue(x);return n[f][z]}),p=r&&Object.entries(r).reduce((f,g)=>{let[x,z]=g;return z===void 0||(f[x]=z),f},{}),d=o==null||(t=o.compoundVariants)===null||t===void 0?void 0:t.reduce((f,g)=>{let{class:x,className:z,...R}=g;return Object.entries(R).every(y=>{let[w,S]=y;return Array.isArray(S)?S.includes({...l,...p}[w]):{...l,...p}[w]===S})?[...f,x,z]:f},[]);return pe(e,a,d,r==null?void 0:r.class,r==null?void 0:r.className)},te="-",Ve=e=>{const o=Ne(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:a=>{const p=a.split(te);return p[0]===""&&p.length!==1&&p.shift(),ye(p,o)||je(a)},getConflictingClassGroupIds:(a,p)=>{const d=r[a]||[];return p&&t[a]?[...d,...t[a]]:d}}},ye=(e,o)=>{var a;if(e.length===0)return o.classGroupId;const r=e[0],t=o.nextPart.get(r),n=t?ye(e.slice(1),t):void 0;if(n)return n;if(o.validators.length===0)return;const l=e.join(te);return(a=o.validators.find(({validator:p})=>p(l)))==null?void 0:a.classGroupId},fe=/^\[(.+)\]$/,je=e=>{if(fe.test(e)){const o=fe.exec(e)[1],r=o==null?void 0:o.substring(0,o.indexOf(":"));if(r)return"arbitrary.."+r}},Ne=e=>{const{theme:o,classGroups:r}=e,t={nextPart:new Map,validators:[]};for(const n in r)Y(r[n],t,n,o);return t},Y=(e,o,r,t)=>{e.forEach(n=>{if(typeof n=="string"){const l=n===""?o:be(o,n);l.classGroupId=r;return}if(typeof n=="function"){if(Te(n)){Y(n(t),o,r,t);return}o.validators.push({validator:n,classGroupId:r});return}Object.entries(n).forEach(([l,a])=>{Y(a,be(o,l),r,t)})})},be=(e,o)=>{let r=e;return o.split(te).forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},Te=e=>e.isThemeGetter,Oe=e=>{if(e<1)return{get:()=>{},set:()=>{}};let o=0,r=new Map,t=new Map;const n=(l,a)=>{r.set(l,a),o++,o>e&&(o=0,t=r,r=new Map)};return{get(l){let a=r.get(l);if(a!==void 0)return a;if((a=t.get(l))!==void 0)return n(l,a),a},set(l,a){r.has(l)?r.set(l,a):n(l,a)}}},ee="!",re=":",Le=re.length,_e=e=>{const{prefix:o,experimentalParseClassName:r}=e;let t=n=>{const l=[];let a=0,p=0,d=0,f;for(let y=0;y<n.length;y++){let w=n[y];if(a===0&&p===0){if(w===re){l.push(n.slice(d,y)),d=y+Le;continue}if(w==="/"){f=y;continue}}w==="["?a++:w==="]"?a--:w==="("?p++:w===")"&&p--}const g=l.length===0?n:n.substring(d),x=Fe(g),z=x!==g,R=f&&f>d?f-d:void 0;return{modifiers:l,hasImportantModifier:z,baseClassName:x,maybePostfixModifierPosition:R}};if(o){const n=o+re,l=t;t=a=>a.startsWith(n)?l(a.substring(n.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(r){const n=t;t=l=>r({className:l,parseClassName:n})}return t},Fe=e=>e.endsWith(ee)?e.substring(0,e.length-1):e.startsWith(ee)?e.substring(1):e,We=e=>{const o=Object.fromEntries(e.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;const n=[];let l=[];return t.forEach(a=>{a[0]==="["||o[a]?(n.push(...l.sort(),a),l=[]):l.push(a)}),n.push(...l.sort()),n}},Be=e=>({cache:Oe(e.cacheSize),parseClassName:_e(e),sortModifiers:We(e),...Ve(e)}),$e=/\s+/,Ue=(e,o)=>{const{parseClassName:r,getClassGroupId:t,getConflictingClassGroupIds:n,sortModifiers:l}=o,a=[],p=e.trim().split($e);let d="";for(let f=p.length-1;f>=0;f-=1){const g=p[f],{isExternal:x,modifiers:z,hasImportantModifier:R,baseClassName:y,maybePostfixModifierPosition:w}=r(g);if(x){d=g+(d.length>0?" "+d:d);continue}let S=!!w,P=t(S?y.substring(0,w):y);if(!P){if(!S){d=g+(d.length>0?" "+d:d);continue}if(P=t(y),!P){d=g+(d.length>0?" "+d:d);continue}S=!1}const _=l(z).join(":"),F=R?_+ee:_,V=F+P;if(a.includes(V))continue;a.push(V);const j=n(P,S);for(let c=0;c<j.length;++c){const C=j[c];a.push(F+C)}d=g+(d.length>0?" "+d:d)}return d};function He(){let e=0,o,r,t="";for(;e<arguments.length;)(o=arguments[e++])&&(r=we(o))&&(t&&(t+=" "),t+=r);return t}const we=e=>{if(typeof e=="string")return e;let o,r="";for(let t=0;t<e.length;t++)e[t]&&(o=we(e[t]))&&(r&&(r+=" "),r+=o);return r};function qe(e,...o){let r,t,n,l=a;function a(d){const f=o.reduce((g,x)=>x(g),e());return r=Be(f),t=r.cache.get,n=r.cache.set,l=p,p(d)}function p(d){const f=t(d);if(f)return f;const g=Ue(d,r);return n(d,g),g}return function(){return l(He.apply(null,arguments))}}const m=e=>{const o=r=>r[e]||[];return o.isThemeGetter=!0,o},ke=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ze=/^\((?:(\w[\w-]*):)?(.+)\)$/i,De=/^\d+\/\d+$/,Je=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ke=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Xe=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ze=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Qe=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,T=e=>De.test(e),u=e=>!!e&&!Number.isNaN(Number(e)),E=e=>!!e&&Number.isInteger(Number(e)),ge=e=>e.endsWith("%")&&u(e.slice(0,-1)),M=e=>Je.test(e),Ye=()=>!0,er=e=>Ke.test(e)&&!Xe.test(e),oe=()=>!1,rr=e=>Ze.test(e),tr=e=>Qe.test(e),or=e=>!s(e)&&!i(e),nr=e=>O(e,Ae,oe),s=e=>ke.test(e),G=e=>O(e,Me,er),Z=e=>O(e,gr,u),sr=e=>O(e,Ce,oe),ir=e=>O(e,Se,tr),lr=e=>O(e,oe,rr),i=e=>ze.test(e),D=e=>L(e,Me),ar=e=>L(e,mr),cr=e=>L(e,Ce),dr=e=>L(e,Ae),ur=e=>L(e,Se),pr=e=>L(e,hr,!0),O=(e,o,r)=>{const t=ke.exec(e);return t?t[1]?o(t[1]):r(t[2]):!1},L=(e,o,r=!1)=>{const t=ze.exec(e);return t?t[1]?o(t[1]):r:!1},Ce=e=>e==="position",fr=new Set(["image","url"]),Se=e=>fr.has(e),br=new Set(["length","size","percentage"]),Ae=e=>br.has(e),Me=e=>e==="length",gr=e=>e==="number",mr=e=>e==="family-name",hr=e=>e==="shadow",vr=()=>{const e=m("color"),o=m("font"),r=m("text"),t=m("font-weight"),n=m("tracking"),l=m("leading"),a=m("breakpoint"),p=m("container"),d=m("spacing"),f=m("radius"),g=m("shadow"),x=m("inset-shadow"),z=m("drop-shadow"),R=m("blur"),y=m("perspective"),w=m("aspect"),S=m("ease"),P=m("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],F=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],V=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],c=()=>[i,s,d],C=()=>[T,"full","auto",...c()],ne=()=>[E,"none","subgrid",i,s],se=()=>["auto",{span:["full",E,i,s]},i,s],B=()=>[E,"auto",i,s],ie=()=>["auto","min","max","fr",i,s],J=()=>["start","end","center","between","around","evenly","stretch","baseline"],N=()=>["start","end","center","stretch"],A=()=>["auto",...c()],I=()=>[T,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...c()],b=()=>[e,i,s],K=()=>[ge,G],h=()=>["","none","full",f,i,s],k=()=>["",u,D,G],$=()=>["solid","dashed","dotted","double"],le=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ae=()=>["","none",R,i,s],ce=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",i,s],U=()=>["none",u,i,s],H=()=>["none",u,i,s],X=()=>[u,i,s],q=()=>[T,"full",...c()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[M],breakpoint:[M],color:[Ye],container:[M],"drop-shadow":[M],ease:["in","out","in-out"],font:[or],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[M],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[M],shadow:[M],spacing:["px",u],text:[M],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",T,s,i,w]}],container:["container"],columns:[{columns:[u,s,i,p]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...F(),s,i]}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[E,"auto",i,s]}],basis:[{basis:[T,"full","auto",p,...c()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[u,T,"auto","initial","none",s]}],grow:[{grow:["",u,i,s]}],shrink:[{shrink:["",u,i,s]}],order:[{order:[E,"first","last","none",i,s]}],"grid-cols":[{"grid-cols":ne()}],"col-start-end":[{col:se()}],"col-start":[{"col-start":B()}],"col-end":[{"col-end":B()}],"grid-rows":[{"grid-rows":ne()}],"row-start-end":[{row:se()}],"row-start":[{"row-start":B()}],"row-end":[{"row-end":B()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ie()}],"auto-rows":[{"auto-rows":ie()}],gap:[{gap:c()}],"gap-x":[{"gap-x":c()}],"gap-y":[{"gap-y":c()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...N(),"baseline"]}],"align-self":[{self:["auto",...N(),"baseline"]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:c()}],px:[{px:c()}],py:[{py:c()}],ps:[{ps:c()}],pe:[{pe:c()}],pt:[{pt:c()}],pr:[{pr:c()}],pb:[{pb:c()}],pl:[{pl:c()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":c()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":c()}],"space-y-reverse":["space-y-reverse"],size:[{size:I()}],w:[{w:[p,"screen",...I()]}],"min-w":[{"min-w":[p,"screen","none",...I()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[a]},...I()]}],h:[{h:["screen",...I()]}],"min-h":[{"min-h":["screen","none",...I()]}],"max-h":[{"max-h":["screen",...I()]}],"font-size":[{text:["base",r,D,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,i,Z]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ge,s]}],"font-family":[{font:[ar,s,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,i,s]}],"line-clamp":[{"line-clamp":[u,"none",i,Z]}],leading:[{leading:[l,...c()]}],"list-image":[{"list-image":["none",i,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",i,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:b()}],"text-color":[{text:b()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:[u,"from-font","auto",i,G]}],"text-decoration-color":[{decoration:b()}],"underline-offset":[{"underline-offset":[u,"auto",i,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:c()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",i,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",i,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...F(),cr,sr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",dr,nr]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},E,i,s],radial:["",i,s],conic:[E,i,s]},ur,ir]}],"bg-color":[{bg:b()}],"gradient-from-pos":[{from:K()}],"gradient-via-pos":[{via:K()}],"gradient-to-pos":[{to:K()}],"gradient-from":[{from:b()}],"gradient-via":[{via:b()}],"gradient-to":[{to:b()}],rounded:[{rounded:h()}],"rounded-s":[{"rounded-s":h()}],"rounded-e":[{"rounded-e":h()}],"rounded-t":[{"rounded-t":h()}],"rounded-r":[{"rounded-r":h()}],"rounded-b":[{"rounded-b":h()}],"rounded-l":[{"rounded-l":h()}],"rounded-ss":[{"rounded-ss":h()}],"rounded-se":[{"rounded-se":h()}],"rounded-ee":[{"rounded-ee":h()}],"rounded-es":[{"rounded-es":h()}],"rounded-tl":[{"rounded-tl":h()}],"rounded-tr":[{"rounded-tr":h()}],"rounded-br":[{"rounded-br":h()}],"rounded-bl":[{"rounded-bl":h()}],"border-w":[{border:k()}],"border-w-x":[{"border-x":k()}],"border-w-y":[{"border-y":k()}],"border-w-s":[{"border-s":k()}],"border-w-e":[{"border-e":k()}],"border-w-t":[{"border-t":k()}],"border-w-r":[{"border-r":k()}],"border-w-b":[{"border-b":k()}],"border-w-l":[{"border-l":k()}],"divide-x":[{"divide-x":k()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":k()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...$(),"hidden","none"]}],"divide-style":[{divide:[...$(),"hidden","none"]}],"border-color":[{border:b()}],"border-color-x":[{"border-x":b()}],"border-color-y":[{"border-y":b()}],"border-color-s":[{"border-s":b()}],"border-color-e":[{"border-e":b()}],"border-color-t":[{"border-t":b()}],"border-color-r":[{"border-r":b()}],"border-color-b":[{"border-b":b()}],"border-color-l":[{"border-l":b()}],"divide-color":[{divide:b()}],"outline-style":[{outline:[...$(),"none","hidden"]}],"outline-offset":[{"outline-offset":[u,i,s]}],"outline-w":[{outline:["",u,D,G]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",g,pr,lr]}],"shadow-color":[{shadow:b()}],"inset-shadow":[{"inset-shadow":["none",i,s,x]}],"inset-shadow-color":[{"inset-shadow":b()}],"ring-w":[{ring:k()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:b()}],"ring-offset-w":[{"ring-offset":[u,G]}],"ring-offset-color":[{"ring-offset":b()}],"inset-ring-w":[{"inset-ring":k()}],"inset-ring-color":[{"inset-ring":b()}],opacity:[{opacity:[u,i,s]}],"mix-blend":[{"mix-blend":[...le(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":le()}],filter:[{filter:["","none",i,s]}],blur:[{blur:ae()}],brightness:[{brightness:[u,i,s]}],contrast:[{contrast:[u,i,s]}],"drop-shadow":[{"drop-shadow":["","none",z,i,s]}],grayscale:[{grayscale:["",u,i,s]}],"hue-rotate":[{"hue-rotate":[u,i,s]}],invert:[{invert:["",u,i,s]}],saturate:[{saturate:[u,i,s]}],sepia:[{sepia:["",u,i,s]}],"backdrop-filter":[{"backdrop-filter":["","none",i,s]}],"backdrop-blur":[{"backdrop-blur":ae()}],"backdrop-brightness":[{"backdrop-brightness":[u,i,s]}],"backdrop-contrast":[{"backdrop-contrast":[u,i,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",u,i,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u,i,s]}],"backdrop-invert":[{"backdrop-invert":["",u,i,s]}],"backdrop-opacity":[{"backdrop-opacity":[u,i,s]}],"backdrop-saturate":[{"backdrop-saturate":[u,i,s]}],"backdrop-sepia":[{"backdrop-sepia":["",u,i,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":c()}],"border-spacing-x":[{"border-spacing-x":c()}],"border-spacing-y":[{"border-spacing-y":c()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",i,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[u,"initial",i,s]}],ease:[{ease:["linear","initial",S,i,s]}],delay:[{delay:[u,i,s]}],animate:[{animate:["none",P,i,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,i,s]}],"perspective-origin":[{"perspective-origin":ce()}],rotate:[{rotate:U()}],"rotate-x":[{"rotate-x":U()}],"rotate-y":[{"rotate-y":U()}],"rotate-z":[{"rotate-z":U()}],scale:[{scale:H()}],"scale-x":[{"scale-x":H()}],"scale-y":[{"scale-y":H()}],"scale-z":[{"scale-z":H()}],"scale-3d":["scale-3d"],skew:[{skew:X()}],"skew-x":[{"skew-x":X()}],"skew-y":[{"skew-y":X()}],transform:[{transform:[i,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:ce()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:q()}],"translate-x":[{"translate-x":q()}],"translate-y":[{"translate-y":q()}],"translate-z":[{"translate-z":q()}],"translate-none":["translate-none"],accent:[{accent:b()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:b()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",i,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":c()}],"scroll-mx":[{"scroll-mx":c()}],"scroll-my":[{"scroll-my":c()}],"scroll-ms":[{"scroll-ms":c()}],"scroll-me":[{"scroll-me":c()}],"scroll-mt":[{"scroll-mt":c()}],"scroll-mr":[{"scroll-mr":c()}],"scroll-mb":[{"scroll-mb":c()}],"scroll-ml":[{"scroll-ml":c()}],"scroll-p":[{"scroll-p":c()}],"scroll-px":[{"scroll-px":c()}],"scroll-py":[{"scroll-py":c()}],"scroll-ps":[{"scroll-ps":c()}],"scroll-pe":[{"scroll-pe":c()}],"scroll-pt":[{"scroll-pt":c()}],"scroll-pr":[{"scroll-pr":c()}],"scroll-pb":[{"scroll-pb":c()}],"scroll-pl":[{"scroll-pl":c()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",i,s]}],fill:[{fill:["none",...b()]}],"stroke-w":[{stroke:[u,D,G,Z]}],stroke:[{stroke:["none",...b()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},xr=qe(vr);function yr(...e){return xr(xe(e))}const wr=Ge("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Cr({className:e,variant:o,size:r,asChild:t=!1,...n}){const l=t?he:"button";return W.jsx(l,{"data-slot":"button",className:yr(wr({variant:o,size:r,className:e})),...n})}export{Cr as B,he as S,Re as a,Ge as b,yr as c,zr as u};
