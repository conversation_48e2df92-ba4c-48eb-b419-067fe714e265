import{K as s,j as e,L as a,$ as t}from"./app-BWHLaRS2.js";import{M as l}from"./menorah-icon-CiFkzgb7.js";import{B as r}from"./book-open-DxiZ0b6m.js";import{H as d}from"./heart-d4DNJW_g.js";import{U as i}from"./users-BHOsrKYW.js";function u(){const{auth:o}=s().props;return e.jsxs(e.Fragment,{children:[e.jsxs(a,{title:"Welcome to SOP Quotes",children:[e.jsx("link",{rel:"preconnect",href:"https://fonts.bunny.net"}),e.jsx("link",{href:"https://fonts.bunny.net/css?family=instrument-sans:400,500,600",rel:"stylesheet"})]}),e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900",children:[e.jsx("header",{className:"w-full px-6 py-4",children:e.jsxs("nav",{className:"flex items-center justify-between max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600",children:e.jsx(r,{className:"h-6 w-6 text-white"})}),e.jsx("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Lapota SOP"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(t,{href:"/quotes",className:"inline-block rounded-lg px-6 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400",children:"Browse Quotes"}),e.jsx(t,{href:"/about",className:"inline-block rounded-lg px-6 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400",children:"About"})]})]})}),e.jsx("main",{className:"flex-1 flex items-center justify-center px-6 py-12",children:e.jsxs("div",{className:"max-w-6xl mx-auto text-center",children:[e.jsxs("div",{className:"mb-16",children:[e.jsxs("h1",{className:"text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6",children:["Find ",e.jsx("span",{className:"text-blue-600",children:"Inspiration"})," in God's Word"]}),e.jsx("p",{className:"text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto",children:"Discover daily inspiration through Christian quotes, Bible verses, and spiritual resources to strengthen your faith journey."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxs(t,{href:"/quotes/daily",className:"inline-flex items-center justify-center rounded-lg bg-blue-600 px-8 py-4 text-lg font-medium text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600",children:[e.jsx(l,{className:"h-5 w-5 mr-2 bg-white rounded-[5px]"}),"Daily Quote"]}),e.jsxs(t,{href:"/quotes",className:"inline-flex items-center justify-center rounded-lg border border-blue-200 bg-white px-8 py-4 text-lg font-medium text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700",children:[e.jsx(r,{className:"h-5 w-5 mr-2"}),"Browse Quotes"]}),e.jsxs(t,{href:"/about",className:"inline-flex items-center justify-center rounded-lg border border-green-200 bg-white px-8 py-4 text-lg font-medium text-green-600 hover:bg-green-50 dark:border-green-800 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700",children:[e.jsx(d,{className:"h-5 w-5 mr-2"}),"About Me"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:e.jsx(r,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Inspiring Quotes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Discover thousands of uplifting quotes from the Bible and renowned Christian authors to encourage your daily walk with God."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsx("div",{className:"bg-green-100 dark:bg-green-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:e.jsx(r,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Personal Favorites"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Save your favorite quotes and verses to create your own personal collection of spiritual encouragement."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsx("div",{className:"bg-purple-100 dark:bg-purple-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:e.jsx(i,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Community Driven"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Join a community of believers sharing wisdom and encouragement through God's timeless truths."})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Start Your Spiritual Journey Today"}),e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-6",children:"Find daily encouragement through God's Word and join our community of believers."}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:e.jsxs(t,{href:"/quotes",className:"inline-flex items-center justify-center rounded-lg bg-blue-600 px-8 py-4 text-lg font-medium text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600",children:[e.jsx(r,{className:"h-5 w-5 mr-2"}),"Explore Quotes"]})})]})]})}),e.jsx("footer",{className:"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4 md:mb-0",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-blue-600 to-indigo-600",children:e.jsx(r,{className:"h-4 w-4 text-white"})}),e.jsx("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"SOP Quotes"})]}),e.jsxs("div",{className:"flex space-x-6",children:[e.jsx(t,{href:"/quotes",className:"text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"Quotes"}),e.jsx(t,{href:"/quotes/daily",className:"text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"Daily Quote"}),e.jsx(t,{href:"/about",className:"text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400",children:"About"})]})]}),e.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 text-center",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"© 2024 LK Soft Development. Spreading God's love through His Word."})})]})})]})]})}export{u as default};
