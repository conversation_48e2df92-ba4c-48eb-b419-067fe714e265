import { Link, usePage } from '@inertiajs/react';
import { BookOpen, Users, Settings, BarChart3, FileText, Volume2, Plus, Menu, X } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import FlashMessage from '@/components/ui/flash-message';
import { Breadcrumbs } from '@/components/breadcrumbs';


export default function AdminLayout({ children }) {
    const { auth } = usePage().props;
    const [sidebarOpen, setSidebarOpen] = useState(false);

    const navigation = [
        { name: 'Dashboard', href: '/admin', icon: BarChart3 },
        { name: 'Quotes', href: '/admin/quotes', icon: BookOpen },
        { name: 'Categories', href: '/admin/categories', icon: FileText },
        { name: 'Media', href: '/admin/media', icon: Volume2 },
        { name: 'Users', href: '/admin/users', icon: Users },
        { name: 'Settings', href: '/admin/settings', icon: Settings },
    ];

    // Build breadcrumbs and page description from URL
    const location = usePage().props?.ziggy?.location || '';
    let pathname = '/admin';
    try {
        pathname = new URL(location).pathname || '/admin';
    } catch { }

    const buildBreadcrumbs = (path) => {
        const segments = path.split('/').filter(Boolean);
        const items = [];

        // Always start with Dashboard for admin-related pages
        items.push({ title: 'Dashboard', href: '/admin' });

        if (segments[0] === 'admin') {
            // quotes, users, categories, media, settings
            if (segments[1]) {
                const map = {
                    quotes: 'Quotes',
                    users: 'Users',
                    categories: 'Categories',
                    media: 'Media',
                    settings: 'Settings',
                };
                const second = segments[1];
                if (map[second]) items.push({ title: map[second], href: `/admin/${second}` });
            }
            // actions: create/edit
            if (segments.includes('create')) items.push({ title: 'Create', href: '#' });
            if (segments.includes('edit')) items.push({ title: 'Edit', href: '#' });
        } else if (segments[0] === 'profile' || segments[0] === 'password' || (segments[0] === 'settings' && segments[1] === 'appearance')) {
            // Settings-related pages
            items.push({ title: 'Settings', href: '/profile' });
            if (segments[0] === 'profile') {
                items.push({ title: 'Profile', href: '/profile' });
            } else if (segments[0] === 'password') {
                items.push({ title: 'Password', href: '/password' });
            } else if (segments[0] === 'settings' && segments[1] === 'appearance') {
                items.push({ title: 'Appearance', href: '/settings/appearance' });
            }
        }
        return items;
    };

    const getDescription = (path) => {
        if (path === '/admin') return 'Manage your SOP Quotes content and users';
        if (path.startsWith('/admin/quotes') && path.endsWith('/create')) return 'Add a new quote';
        if (path.startsWith('/admin/quotes') && path.endsWith('/edit')) return 'Update the quote details';
        if (path.startsWith('/admin/quotes')) return 'Create, edit, and manage all quotes in the system';
        if (path.startsWith('/admin/users')) return 'Manage user accounts and roles';
        if (path.startsWith('/admin/categories')) return 'Organize and manage quote categories';
        if (path.startsWith('/admin/media')) return 'Upload and manage media files';
        if (path.startsWith('/admin/settings')) return 'Configure application settings';
        if (path === '/profile') return 'Manage your profile and account settings';
        if (path === '/password') return 'Change your account password';
        if (path === '/settings/appearance') return 'Customize the application appearance';
        return '';
    };

    const breadcrumbs = buildBreadcrumbs(pathname);
    const pageDescription = getDescription(pathname);

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Mobile sidebar */}
            <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
                <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
                <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800 shadow-xl">
                    <div className="flex h-16 items-center justify-between px-4">
                        <Link href="/" className="flex items-center space-x-2">
                            <BookOpen className="h-8 w-8 text-blue-600" />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">SOP Admin</span>
                        </Link>
                        <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)}>
                            <X className="h-5 w-5" />
                        </Button>
                    </div>
                    <nav className="flex-1 space-y-1 px-2 py-4">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                href={item.href}
                                className="group flex items-center rounded-md px-2 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                            >
                                <item.icon className="mr-3 h-5 w-5" />
                                {item.name}
                            </Link>
                        ))}
                    </nav>
                </div>
            </div>

            {/* Desktop sidebar */}
            <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
                <div className="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
                    <div className="flex h-16 items-center px-4">
                        <Link href="/" className="flex items-center space-x-2">
                            <BookOpen className="h-8 w-8 text-blue-600" />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">SOP Admin</span>
                        </Link>
                    </div>
                    <nav className="flex-1 space-y-1 px-2 py-4">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                href={item.href}
                                className="group flex items-center rounded-md px-2 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                            >
                                <item.icon className="mr-3 h-5 w-5" />
                                {item.name}
                            </Link>
                        ))}
                    </nav>


                </div>
            </div>

            {/* Main content */}
            <div className="lg:pl-64">
                {/* Top navigation */}
                <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:gap-x-6 sm:px-6 lg:px-8">
                    <Button
                        variant="ghost"
                        size="sm"
                        className="lg:hidden"
                        onClick={() => setSidebarOpen(true)}
                    >
                        <Menu className="h-5 w-5" />
                    </Button>

                    <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                        <div className="flex flex-1 items-center">
                            <Breadcrumbs breadcrumbs={breadcrumbs} />
                        </div>
                        <div className="flex items-center gap-x-4 lg:gap-x-6">
                            <AppearanceToggleDropdown />

                            {/* Profile dropdown */}
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="relative">
                                        <span className="sr-only">Open user menu</span>
                                        <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                            <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                                                {auth.user?.name?.charAt(0) || 'U'}
                                            </span>
                                        </div>
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem asChild>
                                        <Link href={route('profile.edit')}>Settings</Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild>
                                        <Link href="/">View Site</Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild>
                                        <Link href="/logout" method="post">Sign out</Link>
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>



                {/* Page content */}
                <main className="py-10">
                    <div className="px-4 sm:px-6 lg:px-8">
                        {children}
                    </div>
                </main>
            </div>

            {/* Flash Messages */}
            <FlashMessage />
        </div>
    );
}
