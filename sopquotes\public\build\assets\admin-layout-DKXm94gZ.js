import{u as y,j as e,K as N,r as b,$ as r}from"./app-BWHLaRS2.js";import{B as t}from"./button-CUovRkQ3.js";import{D as f,a as j,b as u,c as n,X as k,F as v}from"./flash-message-BOB8jroJ.js";import{S as m,M as g,a as p}from"./sun-Bmefm2yw.js";import{c as w,B as x}from"./book-open-DxiZ0b6m.js";import{F as C,V as M}from"./volume-2-DIbtoXia.js";import{U as S}from"./users-BHOsrKYW.js";import{S as A}from"./settings-FGMHgWsK.js";import{M as D}from"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],O=w("ChartColumn",z);function V({className:i="",...c}){const{appearance:d,updateAppearance:a}=y(),l=()=>{switch(d){case"dark":return e.jsx(g,{className:"h-5 w-5"});case"light":return e.jsx(m,{className:"h-5 w-5"});default:return e.jsx(p,{className:"h-5 w-5"})}};return e.jsx("div",{className:i,...c,children:e.jsxs(f,{children:[e.jsx(j,{asChild:!0,children:e.jsxs(t,{variant:"ghost",size:"icon",className:"h-9 w-9 rounded-md",children:[l(),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),e.jsxs(u,{align:"end",children:[e.jsx(n,{onClick:()=>a("light"),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(m,{className:"h-5 w-5"}),"Light"]})}),e.jsx(n,{onClick:()=>a("dark"),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(g,{className:"h-5 w-5"}),"Dark"]})}),e.jsx(n,{onClick:()=>a("system"),children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5"}),"System"]})})]})]})})}function _({children:i}){var h,o;const{auth:c}=N().props,[d,a]=b.useState(!1),l=[{name:"Dashboard",href:"/admin",icon:O},{name:"Quotes",href:"/admin/quotes",icon:x},{name:"Categories",href:"/admin/categories",icon:C},{name:"Media",href:"/admin/media",icon:M},{name:"Users",href:"/admin/users",icon:S},{name:"Settings",href:"/admin/settings",icon:A}];return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsxs("div",{className:`fixed inset-0 z-50 lg:hidden ${d?"block":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>a(!1)}),e.jsxs("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800 shadow-xl",children:[e.jsxs("div",{className:"flex h-16 items-center justify-between px-4",children:[e.jsxs(r,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(x,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"SOP Admin"})]}),e.jsx(t,{variant:"ghost",size:"sm",onClick:()=>a(!1),children:e.jsx(k,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:l.map(s=>e.jsxs(r,{href:s.href,className:"group flex items-center rounded-md px-2 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white",children:[e.jsx(s.icon,{className:"mr-3 h-5 w-5"}),s.name]},s.name))})]})]}),e.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:e.jsxs("div",{className:"flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700",children:[e.jsx("div",{className:"flex h-16 items-center px-4",children:e.jsxs(r,{href:"/",className:"flex items-center space-x-2",children:[e.jsx(x,{className:"h-8 w-8 text-blue-600"}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"SOP Admin"})]})}),e.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:l.map(s=>e.jsxs(r,{href:s.href,className:"group flex items-center rounded-md px-2 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white",children:[e.jsx(s.icon,{className:"mr-3 h-5 w-5"}),s.name]},s.name))})]})}),e.jsxs("div",{className:"lg:pl-64",children:[e.jsxs("div",{className:"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:gap-x-6 sm:px-6 lg:px-8",children:[e.jsx(t,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>a(!0),children:e.jsx(D,{className:"h-5 w-5"})}),e.jsxs("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:[e.jsx("div",{className:"flex flex-1"}),e.jsxs("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:[e.jsx(V,{}),e.jsxs(f,{children:[e.jsx(j,{asChild:!0,children:e.jsxs(t,{variant:"ghost",size:"sm",className:"relative",children:[e.jsx("span",{className:"sr-only",children:"Open user menu"}),e.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:((o=(h=c.user)==null?void 0:h.name)==null?void 0:o.charAt(0))||"U"})})]})}),e.jsxs(u,{align:"end",children:[e.jsx(n,{asChild:!0,children:e.jsx(r,{href:"/profile",children:"Profile"})}),e.jsx(n,{asChild:!0,children:e.jsx(r,{href:"/",children:"View Site"})}),e.jsx(n,{asChild:!0,children:e.jsx(r,{href:"/logout",method:"post",children:"Sign out"})})]})]})]})]})]}),e.jsx("main",{className:"py-10",children:e.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:i})})]}),e.jsx(v,{})]})}export{_ as A};
