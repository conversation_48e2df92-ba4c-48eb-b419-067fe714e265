import { Link } from '@inertiajs/react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import SOPLogo from '@/components/sop-logo';

export default function AuthSimpleLayout({ children, title, description }) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                    <CardHeader className="space-y-6 pb-8">
                        {/* Logo and Brand */}
                        <div className="flex flex-col items-center space-y-4">
                            <Link href={route('home')} className="flex flex-col items-center gap-3 group">
                                <SOPLogo size="md" showGlow={true} />
                                <div className="text-center">
                                    <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                        SOP Quotes
                                    </span>
                                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                        Spirit of Prophecy
                                    </p>
                                </div>
                            </Link>
                        </div>

                        {/* Title and Description */}
                        <div className="space-y-2 text-center">
                            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
                            <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">{description}</p>
                        </div>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        {children}
                    </CardContent>
                </Card>

                {/* Footer */}
                <div className="mt-8 text-center">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                        Discover wisdom and inspiration from God's Word
                    </p>
                </div>
            </div>
        </div>
    );
}
