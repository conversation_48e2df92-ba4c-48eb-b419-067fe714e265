import React, { useRef, useState } from 'react';
import { Download, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import html2canvas from 'html2canvas';

export default function QuoteImageGenerator({ quote, children, className = "" }) {
    const [isGenerating, setIsGenerating] = useState(false);
    const quoteRef = useRef(null);

    const generateImage = async () => {
        setIsGenerating(true);

        try {
            // Create a completely isolated iframe to avoid CSS conflicts
            const iframe = document.createElement('iframe');
            iframe.style.position = 'fixed';
            iframe.style.left = '-9999px';
            iframe.style.top = '-9999px';
            iframe.style.width = '800px';
            iframe.style.height = '600px';
            iframe.style.border = 'none';
            iframe.style.zIndex = '-1000';

            document.body.appendChild(iframe);

            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

            // Write complete HTML with inline styles to avoid CSS conflicts
            iframeDoc.open();
            iframeDoc.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                            margin: 0;
                            padding: 0;
                        }
                    </style>
                </head>
                <body>
                    <div style="
                        width: 800px;
                        padding: 60px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 20px;
                        color: white;
                        box-sizing: border-box;
                        min-height: 600px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                    ">
                        <div style="text-align: center;">
                            <div style="margin-bottom: 40px;">
                                <div style="
                                    width: 60px;
                                    height: 60px;
                                    margin: 0 auto 20px auto;
                                    background: rgba(255,255,255,0.2);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 24px;
                                ">
                                    🕯️
                                </div>
                                <h1 style="font-size: 28px; font-weight: bold; margin: 0; color: white;">SOP Quotes</h1>
                                <p style="font-size: 16px; margin: 10px 0 0 0; opacity: 0.9;">Spirit of Prophecy</p>
                            </div>

                            <blockquote style="
                                font-size: 24px;
                                font-style: italic;
                                line-height: 1.6;
                                margin: 40px 0;
                                font-weight: 300;
                                max-width: 600px;
                                margin-left: auto;
                                margin-right: auto;
                            ">
                                "${quote.text.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}"
                            </blockquote>

                            <div style="margin-top: 40px;">
                                ${quote.reference ? `<p style="font-size: 20px; font-weight: bold; margin: 0 0 10px 0; color: #ffd700;">${quote.reference.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}</p>` : ''}
                                ${quote.author ? `<p style="font-size: 18px; margin: 0 0 10px 0; opacity: 0.9;">— ${quote.author.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}</p>` : ''}
                                ${quote.source ? `<p style="font-size: 14px; margin: 0; opacity: 0.8;">Source: ${quote.source.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}</p>` : ''}
                            </div>

                            ${quote.category ? `
                                <div style="margin-top: 30px;">
                                    <span style="
                                        background: rgba(255,255,255,0.2);
                                        padding: 8px 16px;
                                        border-radius: 20px;
                                        font-size: 14px;
                                        font-weight: 500;
                                        display: inline-block;
                                    ">
                                        ${quote.category.name.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}
                                    </span>
                                </div>
                            ` : ''}

                            <div style="
                                margin-top: 40px;
                                padding-top: 30px;
                                border-top: 1px solid rgba(255,255,255,0.3);
                                font-size: 14px;
                                opacity: 0.8;
                            ">
                                Visit sopquotes.com for more inspiration
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `);
            iframeDoc.close();

            // Wait for the iframe to load
            await new Promise(resolve => setTimeout(resolve, 500));

            // Generate the image from the iframe content
            const canvas = await html2canvas(iframeDoc.body, {
                backgroundColor: '#667eea',
                scale: 2,
                useCORS: false,
                allowTaint: false,
                width: 800,
                logging: false,
            });

            // Remove the iframe
            document.body.removeChild(iframe);

            // Convert to blob and download
            canvas.toBlob((blob) => {
                if (blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `sop-quote-${quote.id || 'daily'}.png`;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                } else {
                    throw new Error('Failed to create image blob');
                }
            }, 'image/png', 0.95);

        } catch (error) {
            console.error('Error generating image:', error);
            alert(`Failed to generate image: ${error.message}. Please try again.`);
        } finally {
            setIsGenerating(false);
        }
    };

    return (
        <div className={className}>
            <div ref={quoteRef} style={{ display: 'none' }}>
                {/* Hidden quote content for image generation */}
            </div>

            {children ? (
                <div onClick={generateImage}>
                    {children}
                </div>
            ) : (
                <Button
                    variant="outline"
                    onClick={generateImage}
                    disabled={isGenerating}
                    className="min-w-[140px]"
                >
                    {isGenerating ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                        <Download className="h-4 w-4 mr-2" />
                    )}
                    {isGenerating ? 'Generating...' : 'Generate Image'}
                </Button>
            )}
        </div>
    );
}
