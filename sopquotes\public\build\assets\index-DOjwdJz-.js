import{r as h,j as e,L as F,S as c,$ as d}from"./app-BWHLaRS2.js";import{Q as _}from"./quotes-layout-D8XO_JXV.js";import{B as l}from"./button-CUovRkQ3.js";import{C as u,a as y,c as N}from"./card-3ac6awgC.js";import{B as n}from"./badge-BSmw6aR2.js";import{I as Q}from"./input-B4rX1XfI.js";import{S as T}from"./search-H5umLGXz.js";import{T as B}from"./tag-CF10xzlC.js";import{H as o}from"./heart-d4DNJW_g.js";import{B as g}from"./book-open-DxiZ0b6m.js";import{U as A}from"./user-CmroWLXK.js";import{C as L}from"./calendar-D56_txMz.js";import{F as $,V as z}from"./volume-2-DIbtoXia.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./menu-DfGfLdRU.js";function q({quotes:r,categories:v,filters:f={},auth:x}){const a=Array.isArray(f)?{}:f,[m,b]=h.useState(String(a.search||"")),[i,w]=h.useState(String(a.category||"")),[k,j]=h.useState({}),S=s=>{s.preventDefault(),c.get("/quotes",{search:m,category:i},{preserveState:!0,preserveScroll:!0})},p=s=>{w(s),c.get("/quotes",{search:m,category:s},{preserveState:!0,preserveScroll:!0})},C=async s=>{if(!x.user){alert("Please login to add favorites");return}j(t=>({...t,[s]:!0}));try{(await(await fetch(`/favorites/quote/${s}/toggle`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")}})).json()).success?c.reload({only:["quotes"]}):alert("Failed to update favorite status")}catch(t){console.error("Error toggling favorite:",t),alert("Failed to update favorite status")}finally{j(t=>({...t,[s]:!1}))}};return e.jsxs(_,{children:[e.jsx(F,{title:"Browse Quotes - SOP Quotes"}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Browse Inspirational Quotes"}),e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"Discover wisdom and inspiration from God's Word and Christian authors"})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8",children:[e.jsx("form",{onSubmit:S,className:"space-y-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(Q,{type:"text",placeholder:"Search quotes...",value:m,onChange:s=>b(s.target.value),className:"w-full"})}),e.jsxs(l,{type:"submit",className:"md:w-auto",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Search"]})]})}),e.jsxs("div",{className:"mt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Filter by Category:"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(l,{variant:i===""?"default":"outline",size:"sm",onClick:()=>p(""),children:"All Categories"}),v.map(s=>e.jsxs(l,{variant:i==s.slug?"default":"outline",size:"sm",onClick:()=>p(s.slug),style:{backgroundColor:i==s.slug?s.color:"transparent",borderColor:s.color,color:i==s.slug?"white":s.color},children:[e.jsx(B,{className:"h-3 w-3 mr-1"}),s.name]},s.id))]})]}),x.user&&e.jsxs("div",{className:"mt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Personal Filters:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:e.jsxs(l,{variant:a.favorites?"default":"outline",size:"sm",onClick:()=>{c.get("/quotes",{...a,favorites:a.favorites?"":"1"},{preserveState:!0,preserveScroll:!0})},children:[e.jsx(o,{className:"h-4 w-4 mr-2"}),"My Favorites"]})})]})]}),r.data&&r.data.some(s=>s.is_featured)&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"Featured Quotes"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:r.data.filter(s=>s.is_featured).slice(0,3).map(s=>e.jsxs(u,{className:"h-full hover:shadow-lg transition-shadow duration-200",children:[e.jsxs(y,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs(n,{variant:"default",className:"text-xs",children:[e.jsx(o,{className:"h-3 w-3 mr-1"}),"Featured"]}),s.category&&e.jsx(n,{variant:"secondary",className:"text-xs",style:{backgroundColor:s.category.color+"20",color:s.category.color},children:s.category.name})]}),e.jsxs("blockquote",{className:"text-sm italic text-gray-700 dark:text-gray-300 leading-relaxed",children:['"',s.text.length>120?s.text.substring(0,120)+"...":s.text,'"']})]}),e.jsxs(N,{className:"space-y-3 pt-0",children:[s.reference&&e.jsx("p",{className:"text-xs font-semibold text-blue-600 dark:text-blue-400",children:s.reference}),s.author&&e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:["— ",s.author]}),e.jsx(d,{href:`/quotes/${s.id}`,children:e.jsxs(l,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Read Full Quote"]})})]})]},s.id))})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:a.search||a.category?"Search Results":"All Quotes"}),r.data&&r.data.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.data.map(s=>e.jsxs(u,{className:"h-full hover:shadow-lg transition-shadow duration-200",children:[e.jsxs(y,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.category&&e.jsx(n,{variant:"secondary",className:"text-xs",style:{backgroundColor:s.category.color+"20",color:s.category.color},children:s.category.name}),s.is_featured&&e.jsxs(n,{variant:"default",className:"text-xs",children:[e.jsx(o,{className:"h-3 w-3 mr-1"}),"Featured"]})]}),e.jsxs("blockquote",{className:"text-sm italic text-gray-700 dark:text-gray-300 leading-relaxed",children:['"',s.text.length>120?s.text.substring(0,120)+"...":s.text,'"']})]}),e.jsxs(N,{className:"space-y-3 pt-0",children:[s.reference&&e.jsx("p",{className:"text-xs font-semibold text-blue-600 dark:text-blue-400",children:s.reference}),s.author&&e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:["— ",s.author]}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[s.creator&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(A,{className:"h-3 w-3 mr-1"}),s.creator.name]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{className:"h-3 w-3 mr-1"}),new Date(s.created_at).toLocaleDateString()]})]}),(s.pdf_path||s.audio_path)&&e.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{children:"Available:"}),s.pdf_path&&e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"h-3 w-3 mr-1 text-red-500"}),"PDF"]}),s.audio_path&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-3 w-3 mr-1 text-blue-500"}),"Audio"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{href:`/quotes/${s.id}`,className:"flex-1",children:e.jsxs(l,{variant:"outline",size:"sm",className:"w-full",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Read Full Quote"]})}),x.user&&e.jsx(l,{variant:s.is_favorited?"default":"outline",size:"sm",onClick:()=>C(s.id),disabled:k[s.id],className:"px-3",children:e.jsx(o,{className:`h-4 w-4 ${s.is_favorited?"fill-current":""}`})})]})]})]},s.id))}),r.links&&r.links.length>3&&e.jsx("div",{className:"flex justify-center mt-8",children:e.jsx("div",{className:"flex space-x-2",children:r.links.map((s,t)=>e.jsx(d,{href:s.url||"#",className:`px-3 py-2 text-sm rounded-md ${s.active?"bg-blue-600 text-white":s.url?"bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600":"bg-gray-100 dark:bg-gray-700 text-gray-400 cursor-not-allowed"}`,dangerouslySetInnerHTML:{__html:s.label}},t))})})]}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(g,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No quotes found"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:a.search||a.category?"Try adjusting your search or filter criteria.":"No quotes have been added yet."}),(a.search||a.category)&&e.jsx(d,{href:"/quotes",children:e.jsx(l,{variant:"outline",children:"Clear Filters"})})]})]})]})]})}export{q as default};
