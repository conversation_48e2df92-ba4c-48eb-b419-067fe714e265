import { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react';
import { usePage } from '@inertiajs/react';

export default function FlashMessage() {
    const { props } = usePage();
    const [messages, setMessages] = useState([]);

    useEffect(() => {
        const newMessages = [];

        // Check for different types of flash messages
        if (props.flash?.success) {
            newMessages.push({
                id: Date.now() + Math.random(),
                type: 'success',
                message: props.flash.success
            });
        }

        if (props.flash?.error) {
            newMessages.push({
                id: Date.now() + Math.random(),
                type: 'error',
                message: props.flash.error
            });
        }

        if (props.flash?.warning) {
            newMessages.push({
                id: Date.now() + Math.random(),
                type: 'warning',
                message: props.flash.warning
            });
        }

        if (props.flash?.info) {
            newMessages.push({
                id: Date.now() + Math.random(),
                type: 'info',
                message: props.flash.info
            });
        }

        if (newMessages.length > 0) {
            setMessages(prev => [...prev, ...newMessages]);

            // Auto-remove messages after 6 seconds (longer for better UX)
            newMessages.forEach(msg => {
                setTimeout(() => {
                    removeMessage(msg.id);
                }, 6000);
            });
        }
    }, [props.flash]);

    const removeMessage = (id) => {
        setMessages(prev => prev.filter(msg => msg.id !== id));
    };

    const getIcon = (type) => {
        switch (type) {
            case 'success':
                return <CheckCircle className="h-5 w-5" />;
            case 'error':
                return <XCircle className="h-5 w-5" />;
            case 'warning':
                return <AlertCircle className="h-5 w-5" />;
            case 'info':
                return <Info className="h-5 w-5" />;
            default:
                return <Info className="h-5 w-5" />;
        }
    };

    const getStyles = (type) => {
        switch (type) {
            case 'success':
                return 'bg-green-50/95 border-green-300 text-green-900 dark:bg-green-950/95 dark:border-green-700 dark:text-green-100';
            case 'error':
                return 'bg-red-50/95 border-red-300 text-red-900 dark:bg-red-950/95 dark:border-red-700 dark:text-red-100';
            case 'warning':
                return 'bg-yellow-50/95 border-yellow-300 text-yellow-900 dark:bg-yellow-950/95 dark:border-yellow-700 dark:text-yellow-100';
            case 'info':
                return 'bg-blue-50/95 border-blue-300 text-blue-900 dark:bg-blue-950/95 dark:border-blue-700 dark:text-blue-100';
            default:
                return 'bg-gray-50/95 border-gray-300 text-gray-900 dark:bg-gray-950/95 dark:border-gray-700 dark:text-gray-100';
        }
    };

    if (messages.length === 0) return null;

    return (
        <div className="fixed top-4 right-4 z-50 space-y-3">
            {messages.map((msg) => (
                <div
                    key={msg.id}
                    className={`flex items-start p-5 border-2 rounded-xl shadow-2xl max-w-sm backdrop-blur-sm animate-in slide-in-from-right-full duration-500 ${getStyles(msg.type)}`}
                    style={{
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
                    }}
                >
                    <div className="flex-shrink-0 mt-0.5">
                        {getIcon(msg.type)}
                    </div>
                    <div className="ml-4 flex-1 min-w-0">
                        <p className="text-sm font-semibold leading-5">
                            {msg.message}
                        </p>
                        {/* Auto-dismiss indicator */}
                        <div className="mt-2 text-xs opacity-60">
                            Auto-dismiss in 6s
                        </div>
                    </div>
                    <div className="ml-4 flex-shrink-0">
                        <button
                            onClick={() => removeMessage(msg.id)}
                            className="inline-flex rounded-lg p-1.5 hover:bg-black/10 dark:hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current transition-colors duration-200"
                            aria-label="Dismiss notification"
                        >
                            <X className="h-4 w-4" />
                        </button>
                    </div>
                </div>
            ))}
        </div>
    );
}
