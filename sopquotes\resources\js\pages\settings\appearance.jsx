import { Head } from '@inertiajs/react';
import SettingsLayout from '@/layouts/settings/layout';
import AppearanceTabs from '@/components/appearance-tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Palette } from 'lucide-react';

export default function Appearance() {
    return (
        <SettingsLayout>
            <Head title="Appearance Settings" />

            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Palette className="h-5 w-5" />
                        <span>Appearance Settings</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                        Customize the look and feel of the application.
                    </p>
                    <AppearanceTabs />
                </CardContent>
            </Card>
        </SettingsLayout>
    );
}
