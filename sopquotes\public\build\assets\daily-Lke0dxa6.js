import{j as e,L as o,$ as s}from"./app-BWHLaRS2.js";import{M as l}from"./menorah-icon-CiFkzgb7.js";import{S as m,Q as d}from"./quote-image-generator-CVqAk3w_.js";import{Q as n}from"./quotes-layout-D8XO_JXV.js";import{B as t}from"./button-CUovRkQ3.js";import{C as h,a as j,c as p}from"./card-3ac6awgC.js";import{B as i}from"./book-open-DxiZ0b6m.js";import"./loader-circle-Bb-8g7No.js";import"./download-Bv0PHygq.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./user-CmroWLXK.js";import"./menu-DfGfLdRU.js";function B({quote:a,auth:y}){const x=async()=>{if(!a)return;const r={title:"Daily Christian Quote",text:`"${a.text}" ${a.reference?`- ${a.reference}`:""}`,url:window.location.href};try{navigator.share?await navigator.share(r):(await navigator.clipboard.writeText(`${r.text}

${r.url}`),alert("Quote copied to clipboard!"))}catch(c){console.error("Failed to share:",c),alert("Unable to share quote. Please try again.")}};return a?e.jsxs(n,{children:[e.jsx(o,{title:"Daily Quote - SOP Quotes"}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("div",{className:"flex items-center justify-center mb-4",children:[e.jsx(l,{className:"h-8 w-8 text-orange-500 mr-3"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Daily Inspiration"})]}),e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400",children:"Start your day with wisdom from the Spirit of Prophecy"})]}),e.jsxs(h,{className:"mb-8 shadow-lg",children:[e.jsxs(j,{className:"text-center pb-8 bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx(l,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"})}),e.jsxs("blockquote",{className:"text-2xl md:text-3xl italic text-gray-800 dark:text-gray-200 leading-relaxed font-light max-w-3xl mx-auto",children:['"',a.text,'"']})]}),e.jsxs(p,{className:"space-y-6 p-8",children:[e.jsx("div",{className:"text-center space-y-3",children:a.reference&&e.jsx("p",{className:"text-xl font-bold text-blue-700 dark:text-blue-300",children:a.reference})}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-3 pt-4",children:[e.jsxs(t,{variant:"outline",onClick:x,className:"min-w-[100px]",children:[e.jsx(m,{className:"h-4 w-4 mr-2"}),"Share"]}),e.jsx(d,{quote:a}),a.id&&e.jsx(s,{href:`/quotes/${a.id}`,children:e.jsxs(t,{variant:"outline",className:"min-w-[120px]",children:[e.jsx(i,{className:"h-4 w-4 mr-2"}),"View Details"]})})]}),e.jsx("div",{className:"text-center pt-4 border-t border-gray-100 dark:border-gray-700",children:e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Daily quote for ",new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})]})})]})]}),e.jsx("div",{className:"text-center",children:e.jsx(s,{href:"/quotes",children:e.jsxs(t,{variant:"outline",children:[e.jsx(i,{className:"h-4 w-4 mr-2"}),"Browse All Quotes"]})})})]})]}):e.jsxs(n,{children:[e.jsx(o,{title:"Daily Quote - SOP Quotes"}),e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx(l,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No daily quote available"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Please check back later for today's inspirational quote."}),e.jsx(s,{href:"/quotes",children:e.jsxs(t,{variant:"outline",children:[e.jsx(i,{className:"h-4 w-4 mr-2"}),"Browse All Quotes"]})})]})})]})}export{B as default};
