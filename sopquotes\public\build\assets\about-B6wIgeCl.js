import{j as e,L as r}from"./app-BWHLaRS2.js";import{Q as l}from"./quotes-layout-D8XO_JXV.js";import{c as a}from"./book-open-DxiZ0b6m.js";import{H as i}from"./heart-d4DNJW_g.js";import{U as d}from"./users-BHOsrKYW.js";import{M as o}from"./mail-BHQUwL07.js";import"./button-CUovRkQ3.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./user-CmroWLXK.js";import"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],c=a("Book",n);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],t=a("ExternalLink",x);function v({appVersion:s="1.3"}){return e.jsxs(l,{children:[e.jsx(r,{title:"About - Lapota SOP"}),e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:e.jsx("div",{className:"about-page",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"page-header text-center mb-12",children:[e.jsx("h1",{className:"text-5xl font-bold text-gray-900 dark:text-white mb-4",children:"About Lapota SOP"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Your digital companion for exploring the Spirit of Prophecy writings"})]}),e.jsxs("div",{className:"about-content space-y-12",children:[e.jsxs("section",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 p-3 rounded-full mr-4",children:e.jsx(c,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"My Mission"})]}),e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed",children:"Lapota SOP is dedicated to making the profound wisdom of Ellen G. White's Spirit of Prophecy writings easily accessible to everyone. I believe these inspired messages contain timeless guidance for modern Christian living."})]}),e.jsxs("section",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"bg-red-100 dark:bg-red-900 p-3 rounded-full mr-4",children:e.jsx(i,{className:"h-8 w-8 text-red-600 dark:text-red-400"})}),e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"What I Offer"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Organized Categories"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Quotes organized by topics like Youth, Marriage, Family, Salvation, and more."})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Powerful Search"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Find specific quotes quickly by searching through content and references."})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Personal Favorites"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Save quotes that inspire you for easy access and reflection."})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Easy Sharing"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Share meaningful quotes with friends and family through various platforms."})]})]})]}),e.jsxs("section",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"bg-purple-100 dark:bg-purple-900 p-3 rounded-full mr-4",children:e.jsx(d,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"})}),e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"About the Developer"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("p",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed",children:["Lapota SOP was created by ",e.jsx("strong",{className:"text-gray-900 dark:text-white",children:"Lasmon Kapota"}),", a passionate developer committed to using technology to spread spiritual inspiration and biblical truth. This project represents a labor of love to make these precious writings more accessible to the global community."]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Get in Touch"}),e.jsxs("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300",children:[e.jsx(o,{className:"h-5 w-5"}),e.jsx("span",{children:"<EMAIL>"})]})]})]})]}),e.jsxs("section",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"Technical Information"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed",children:"This application is built with modern web technologies including React, React Router, and Lucide React icons. The quotes are currently stored in JSON format, with plans to migrate to a more robust database solution using Laravel and SQLite for enhanced performance and features."}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Version"}),e.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Last Updated"}),e.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"December 2024"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Total Quotes"}),e.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:1250 .toLocaleString()})]})]})})]})]}),e.jsxs("section",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Disclaimer"}),e.jsx("p",{className:"text-lg text-gray-700 dark:text-gray-300 leading-relaxed",children:"The quotes in this application are from the published works of Ellen G. White. While every effort has been made to ensure accuracy, users are encouraged to verify quotes with original sources. This application is not officially endorsed by the Ellen G. White Estate."})]}),e.jsxs("section",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"Additional Resources"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("a",{href:"https://egwwritings.org",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 w-full transition-colors",children:[e.jsx(t,{className:"h-5 w-5"}),e.jsx("span",{className:"text-lg font-medium",children:"Official EGW Writings Website"})]}),e.jsxs("a",{href:"https://whiteestate.org",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 w-full transition-colors",children:[e.jsx(t,{className:"h-5 w-5"}),e.jsx("span",{className:"text-lg font-medium",children:"Ellen G. White Estate"})]})]})]})]})]})})})})]})}export{v as default};
