<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the quotes created by this user.
     */
    public function quotes(): HasMany
    {
        return $this->hasMany(Quote::class, 'created_by');
    }

    /**
     * Get the books uploaded by this user.
     */
    public function books(): HasMany
    {
        return $this->hasMany(Book::class, 'uploaded_by');
    }

    /**
     * Get the media uploaded by this user.
     */
    public function media(): HasMany
    {
        return $this->hasMany(Media::class, 'uploaded_by');
    }

    /**
     * Get the user's favorites.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(UserFavorite::class);
    }

    /**
     * Get the user's downloads.
     */
    public function downloads(): HasMany
    {
        return $this->hasMany(UserDownload::class);
    }

    /**
     * Get the user's reading history.
     */
    public function readingHistory(): HasMany
    {
        return $this->hasMany(UserReadingHistory::class);
    }

    /**
     * Check if the user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if the user can manage content.
     */
    public function canManageContent(): bool
    {
        return $this->isAdmin();
    }
}
