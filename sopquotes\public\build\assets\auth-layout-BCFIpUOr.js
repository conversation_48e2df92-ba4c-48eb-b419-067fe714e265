import{j as e,$ as o}from"./app-BWHLaRS2.js";import{C as n,a as c,c as d}from"./card-3ac6awgC.js";import{B as x}from"./book-open-DxiZ0b6m.js";function m({size:s="md",className:t="",showGlow:a=!1}){const r={sm:"h-8 w-8",md:"h-16 w-16",lg:"h-20 w-20",xl:"h-24 w-24"},l={sm:"h-4 w-4",md:"h-8 w-8",lg:"h-10 w-10",xl:"h-12 w-12"},i={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};return e.jsxs("div",{className:`relative ${t}`,children:[a&&e.jsx("div",{className:"absolute inset-0 bg-blue-600 rounded-full blur-lg opacity-20 group-hover:opacity-30 transition-opacity"}),e.jsxs("div",{className:`relative flex flex-col items-center justify-center ${r[s]} rounded-full bg-gradient-to-br from-blue-600 to-indigo-600 shadow-lg`,children:[e.jsx(x,{className:`${l[s]} text-white mb-0.5`}),e.jsx("span",{className:`${i[s]} font-bold text-white leading-none`,children:"SOPQ"})]})]})}function h({children:s,title:t,description:a}){return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsxs(n,{className:"shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",children:[e.jsxs(c,{className:"space-y-6 pb-8",children:[e.jsx("div",{className:"flex flex-col items-center space-y-4",children:e.jsxs(o,{href:route("home"),className:"flex flex-col items-center gap-3 group",children:[e.jsx(m,{size:"md",showGlow:!0}),e.jsxs("div",{className:"text-center",children:[e.jsx("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"SOP Quotes"}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:"Spirit of Prophecy"})]})]})}),e.jsxs("div",{className:"space-y-2 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:t}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm leading-relaxed",children:a})]})]}),e.jsx(d,{className:"space-y-6",children:s})]}),e.jsx("div",{className:"mt-8 text-center",children:e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Discover wisdom and inspiration from God's Word"})})]})})}function f({children:s,title:t,description:a,...r}){return e.jsx(h,{title:t,description:a,...r,children:s})}export{f as A};
