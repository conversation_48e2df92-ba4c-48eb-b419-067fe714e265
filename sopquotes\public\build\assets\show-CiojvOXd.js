import{r as x,j as e,L as h,$ as l,S as u}from"./app-BWHLaRS2.js";import{S as f,Q as p}from"./quote-image-generator-CVqAk3w_.js";import{Q as j}from"./quotes-layout-D8XO_JXV.js";import{B as r}from"./button-CUovRkQ3.js";import{C as w,a as g,c as N}from"./card-3ac6awgC.js";import{A as b}from"./arrow-left-B-2ALS0F.js";import{c as y,B as d}from"./book-open-DxiZ0b6m.js";import{L as v}from"./loader-circle-Bb-8g7No.js";import{D as n}from"./download-Bv0PHygq.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./user-CmroWLXK.js";import"./menu-DfGfLdRU.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Q=y("RefreshCw",k);function O({quote:a,auth:$}){const[o,i]=x.useState(!1),c=async()=>{const s={title:`Quote from ${a.reference||"SOP Quotes"}`,text:`"${a.text}" ${a.reference?`- ${a.reference}`:""}`,url:window.location.href};try{navigator.share?await navigator.share(s):(await navigator.clipboard.writeText(`${s.text}

${s.url}`),alert("Quote copied to clipboard!"))}catch(t){console.error("Failed to share:",t),alert("Unable to share quote. Please try again.")}},m=async()=>{i(!0);try{const t=await(await fetch("/api/quotes/random")).json();if(t.quote&&t.quote.id)u.visit(`/quotes/${t.quote.id}`,{preserveScroll:!1,onFinish:()=>i(!1)});else throw new Error("No quote received")}catch(s){console.error("Error fetching new quote:",s),alert("Unable to load new quote. Please try again."),i(!1)}};return e.jsxs(j,{children:[e.jsx(h,{title:"Quote - SOP Quotes"}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsx("div",{className:"mb-6",children:e.jsx(l,{href:"/quotes",children:e.jsxs(r,{variant:"outline",size:"sm",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Back to Quotes"]})})}),e.jsxs(w,{className:"mb-8 shadow-lg",children:[e.jsxs(g,{className:"text-center pb-8 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx(d,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),e.jsxs("blockquote",{className:"text-2xl md:text-4xl italic text-gray-800 dark:text-gray-200 leading-relaxed font-light max-w-4xl mx-auto",children:['"',a.text,'"']})]}),e.jsxs(N,{className:"space-y-6 p-8",children:[e.jsxs("div",{className:"text-center space-y-3",children:[a.reference&&e.jsx("p",{className:"text-xl font-bold text-blue-700 dark:text-blue-300",children:a.reference}),a.author&&e.jsxs("p",{className:"text-lg text-gray-700 dark:text-gray-300 font-medium",children:["— ",a.author]}),a.source&&e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:["Source: ",a.source]})]}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-3 pt-4",children:[e.jsxs(r,{variant:"outline",onClick:m,disabled:o,className:"min-w-[120px]",children:[o?e.jsx(v,{className:"h-4 w-4 mr-2 animate-spin"}):e.jsx(Q,{className:"h-4 w-4 mr-2"}),"New Quote"]}),e.jsxs(r,{variant:"outline",onClick:c,className:"min-w-[100px]",children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"Share"]}),e.jsx(p,{quote:a})]}),(a.pdf_path||a.audio_path)&&e.jsxs("div",{className:"border-t pt-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center",children:"Available Media"}),a.audio_path&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"🎵 Listen to Audio"}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[e.jsxs("audio",{controls:!0,className:"w-full",preload:"metadata",children:[e.jsx("source",{src:`/stream/quote/${a.id}/audio`,type:"audio/mpeg"}),"Your browser does not support the audio element."]}),e.jsx("div",{className:"flex justify-center mt-3",children:e.jsx("a",{href:`/download/quote/${a.id}/audio`,className:"inline-flex",children:e.jsxs(r,{variant:"outline",size:"sm",children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Download Audio"]})})})]})]}),a.pdf_path&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"📄 View Document"}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[e.jsx("div",{className:"aspect-[4/3] bg-white rounded border",children:e.jsx("iframe",{src:`/stream/quote/${a.id}/pdf`,className:"w-full h-full rounded",title:"Quote PDF"})}),e.jsx("div",{className:"flex justify-center mt-3",children:e.jsx("a",{href:`/download/quote/${a.id}/pdf`,className:"inline-flex",children:e.jsxs(r,{variant:"outline",size:"sm",children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Download PDF"]})})})]})]})]}),e.jsx("div",{className:"text-center pt-4 border-t border-gray-100 dark:border-gray-700 mt-6",children:e.jsx(l,{href:"/quotes",children:e.jsxs(r,{variant:"outline",children:[e.jsx(d,{className:"h-4 w-4 mr-2"}),"Browse More Quotes"]})})})]})]})]})]})}export{O as default};
