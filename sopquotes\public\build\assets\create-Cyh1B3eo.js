import{m as u,j as e,L as g,$ as y}from"./app-BWHLaRS2.js";import{A as v}from"./admin-layout-DKXm94gZ.js";import{B as i}from"./button-CUovRkQ3.js";import{C as l,a as o,b as d,c as m}from"./card-3ac6awgC.js";import{I as f}from"./input-B4rX1XfI.js";import{L as n}from"./label-DWihlnjb.js";import{T as C,S as x}from"./textarea-DsrDZwdm.js";import{C as N}from"./checkbox-B6mGW3Gq.js";import{A as b}from"./arrow-left-B-2ALS0F.js";import{T as w}from"./tag-CF10xzlC.js";import"./flash-message-BOB8jroJ.js";import"./index-CB7we0-r.js";import"./book-open-DxiZ0b6m.js";import"./sun-Bmefm2yw.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";import"./index-C3-VNe4Y.js";import"./index-BDjL_iy4.js";function O({auth:k}){const{data:a,setData:r,post:h,processing:c,errors:t,reset:p}=u({name:"",description:"",is_active:!0}),j=s=>{s.preventDefault(),h(route("admin.categories.store"))};return e.jsxs(v,{children:[e.jsx(g,{title:"Create Category - Admin Dashboard"}),e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create New Category"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Add a new category to organize quotes"})]}),e.jsx(y,{href:"/admin/categories",children:e.jsxs(i,{variant:"outline",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Back to Categories"]})})]}),e.jsxs(l,{children:[e.jsx(o,{children:e.jsxs(d,{className:"flex items-center space-x-2",children:[e.jsx(w,{className:"h-5 w-5"}),e.jsx("span",{children:"Category Information"})]})}),e.jsx(m,{children:e.jsxs("form",{onSubmit:j,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"name",children:"Category Name *"}),e.jsx(f,{id:"name",value:a.name,onChange:s=>r("name",s.target.value),placeholder:"e.g., Faith, Hope, Love",className:t.name?"border-red-500":""}),t.name&&e.jsx("p",{className:"text-red-500 text-sm",children:t.name}),e.jsx("p",{className:"text-sm text-gray-500",children:"A unique name for this category. The URL slug will be generated automatically."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"description",children:"Description"}),e.jsx(C,{id:"description",value:a.description,onChange:s=>r("description",s.target.value),placeholder:"Describe what types of quotes belong in this category...",rows:3,className:t.description?"border-red-500":""}),t.description&&e.jsx("p",{className:"text-red-500 text-sm",children:t.description}),e.jsx("p",{className:"text-sm text-gray-500",children:"Optional description to help users understand this category."})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{id:"is_active",checked:a.is_active,onCheckedChange:s=>r("is_active",s)}),e.jsx(n,{htmlFor:"is_active",children:"Active Category"})]}),e.jsx("p",{className:"text-sm text-gray-500 ml-6",children:"Active categories are visible to users and can be assigned to quotes."}),e.jsxs("div",{className:"flex justify-end space-x-4 pt-6",children:[e.jsx(i,{type:"button",variant:"outline",onClick:()=>p(),children:"Reset"}),e.jsx(i,{type:"submit",disabled:c,children:c?e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Create Category"]})})]})]})})]}),e.jsxs(l,{children:[e.jsx(o,{children:e.jsx(d,{children:"Category Guidelines"})}),e.jsxs(m,{className:"space-y-3",children:[e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Best Practices:"}),e.jsxs("ul",{className:"space-y-1 list-disc list-inside",children:[e.jsx("li",{children:"Use clear, descriptive names that users will understand"}),e.jsx("li",{children:"Keep category names concise (1-3 words when possible)"}),e.jsx("li",{children:"Avoid overlapping categories that might confuse users"}),e.jsx("li",{children:"Consider the spiritual themes that resonate with your audience"})]})]}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Examples:"}),e.jsxs("ul",{className:"space-y-1 list-disc list-inside",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Faith:"})," Quotes about trust in God and spiritual belief"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Prayer:"})," Quotes about communication with God"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Character:"})," Quotes about Christian character development"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Prophecy:"})," Quotes about end-time events and biblical prophecy"]})]})]})]})]})]})]})}export{O as default};
