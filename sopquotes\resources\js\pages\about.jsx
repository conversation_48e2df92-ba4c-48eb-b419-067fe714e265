import { Head } from '@inertiajs/react';
import { Book, Heart, Users, Mail, ExternalLink } from 'lucide-react';
import QuotesLayout from '@/layouts/quotes-layout';

export default function About({ appVersion = '1.3' }) {
    const totalQuotes = 171; // This would be calculated from actual data

    return (
        <QuotesLayout>
            <Head title="About - Lapota SOP" />
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                    <div className="about-page">
                        <div className="container">
                            {/* Header */}
                            <div className="page-header text-center mb-12">
                                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">About Lapota SOP</h1>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Your digital companion for exploring the Spirit of Prophecy writings
                                </p>
                            </div>

                            {/* Main Content */}
                            <div className="about-content space-y-12">
                                {/* Mission Section */}
                                <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                    <div className="flex items-center mb-6">
                                        <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full mr-4">
                                            <Book className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">My Mission</h2>
                                    </div>
                                    <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                                        Lapota SOP is dedicated to making the profound wisdom of Ellen G. White's
                                        Spirit of Prophecy writings easily accessible to everyone. I believe these
                                        inspired messages contain timeless guidance for modern Christian living.
                                    </p>
                                </section>

                                {/* Features Section */}
                                <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                    <div className="flex items-center mb-6">
                                        <div className="bg-red-100 dark:bg-red-900 p-3 rounded-full mr-4">
                                            <Heart className="h-8 w-8 text-red-600 dark:text-red-400" />
                                        </div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">What the App Offers</h2>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Organized Categories</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Quotes organized by topics like Youth, Marriage, Family, Salvation, and more.</p>
                                        </div>
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Powerful Search</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Find specific quotes quickly by searching through content and references.</p>
                                        </div>
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Personal Favorites</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Save quotes that inspire you for easy access and reflection.</p>
                                        </div>
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Easy Sharing</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Share meaningful quotes with friends and family through various platforms.</p>
                                        </div>
                                    </div>
                                </section>

                                {/* Developer Section */}
                                <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                    <div className="flex items-center mb-6">
                                        <div className="bg-purple-100 dark:bg-purple-900 p-3 rounded-full mr-4">
                                            <Users className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                                        </div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">About the Developer</h2>
                                    </div>
                                    <div className="space-y-6">
                                        <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                                            Lapota SOP was created by <strong className="text-gray-900 dark:text-white">Lasmon Kapota</strong>, a passionate developer
                                            committed to using technology to spread spiritual inspiration and biblical truth.
                                            This project represents a labor of love to make these precious writings more
                                            accessible to the global community.
                                        </p>

                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Get in Touch</h4>
                                            <a href="mailto:<EMAIL>" className="inline-flex items-center space-x-2 text-blue-600 w-full dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                                                <Mail className="h-5 w-5" />
                                                <span><EMAIL></span>
                                            </a>
                                        </div>
                                    </div>
                                </section>

                                {/* Technical Info */}
                                <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Technical Information</h2>
                                    <div className="space-y-6">
                                        <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                                            This application is built with modern web technologies including React, Laravel, Tailwind CSS and Shadcn UI.
                                        </p>

                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <div>
                                                    <p className="text-sm text-gray-500 dark:text-gray-400">Version</p>
                                                    <p className="text-lg font-semibold text-gray-900 dark:text-white">{appVersion}</p>
                                                </div>
                                                <div>
                                                    <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                                                    <p className="text-lg font-semibold text-gray-900 dark:text-white">December 2025</p>
                                                </div>
                                                <div>
                                                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Quotes</p>
                                                    <p className="text-lg font-semibold text-gray-900 dark:text-white">{totalQuotes.toLocaleString()}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>

                                {/* Disclaimer */}
                                <section className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-8">
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Disclaimer</h2>
                                    <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                                        The quotes in this application are from the published works of Ellen G. White.
                                        While every effort has been made to ensure accuracy, users are encouraged to
                                        verify quotes with original sources. This application is not officially
                                        endorsed by the Ellen G. White Estate.
                                    </p>
                                </section>

                                {/* Resources */}
                                <section className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Additional Resources</h2>
                                    <div className="space-y-4">
                                        <a
                                            href="https://egwwritings.org"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="inline-flex items-center space-x-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 w-full transition-colors"
                                        >
                                            <ExternalLink className="h-5 w-5" />
                                            <span className="text-lg font-medium">Official EGW Writings Website</span>
                                        </a>
                                        <a
                                            href="https://whiteestate.org"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="inline-flex items-center space-x-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 w-full transition-colors"
                                        >
                                            <ExternalLink className="h-5 w-5" />
                                            <span className="text-lg font-medium">Ellen G. White Estate</span>
                                        </a>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </QuotesLayout>
    );
}
