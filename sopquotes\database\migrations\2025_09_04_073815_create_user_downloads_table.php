<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_downloads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('downloadable_type'); // Book, Media
            $table->unsignedBigInteger('downloadable_id');
            $table->timestamp('downloaded_at');
            $table->timestamps();
            
            // Polymorphic index
            $table->index(['downloadable_type', 'downloadable_id']);
            $table->index(['user_id', 'downloaded_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_downloads');
    }
};
