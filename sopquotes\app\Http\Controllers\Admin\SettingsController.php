<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index(): Response
    {
        // Authorization is handled by admin middleware

        $settings = Setting::orderBy('group')->orderBy('key')->get()->groupBy('group');

        return Inertia::render('admin/settings/index', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update settings.
     */
    public function update(Request $request)
    {
        $this->authorize('update', Setting::class);

        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'nullable',
            'settings.*.type' => 'required|in:string,boolean,integer,float,json',
            'settings.*.group' => 'required|string',
            'settings.*.description' => 'nullable|string',
            'settings.*.is_public' => 'boolean',
        ]);

        foreach ($validated['settings'] as $settingData) {
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                [
                    'value' => $settingData['value'],
                    'type' => $settingData['type'],
                    'group' => $settingData['group'],
                    'description' => $settingData['description'] ?? null,
                    'is_public' => $settingData['is_public'] ?? false,
                ]
            );
        }

        Setting::clearCache();

        return back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Reset settings to defaults.
     */
    public function reset(Request $request)
    {
        $this->authorize('update', Setting::class);

        $validated = $request->validate([
            'group' => 'required|string',
        ]);

        Setting::where('group', $validated['group'])->delete();
        $this->seedDefaultSettings($validated['group']);

        Setting::clearCache();

        return back()->with('success', 'Settings reset to defaults successfully.');
    }

    /**
     * Seed default settings for a group.
     */
    private function seedDefaultSettings(string $group): void
    {
        $defaults = $this->getDefaultSettings();

        if (isset($defaults[$group])) {
            foreach ($defaults[$group] as $setting) {
                Setting::create($setting);
            }
        }
    }

    /**
     * Get default settings configuration.
     */
    private function getDefaultSettings(): array
    {
        return [
            'general' => [
                [
                    'key' => 'site_name',
                    'value' => 'SOP Quotes',
                    'type' => 'string',
                    'group' => 'general',
                    'description' => 'The name of your website',
                    'is_public' => true,
                ],
                [
                    'key' => 'site_description',
                    'value' => 'Inspirational quotes from the Spirit of Prophecy',
                    'type' => 'string',
                    'group' => 'general',
                    'description' => 'A brief description of your website',
                    'is_public' => true,
                ],
                [
                    'key' => 'quotes_per_page',
                    'value' => '12',
                    'type' => 'integer',
                    'group' => 'general',
                    'description' => 'Number of quotes to display per page',
                    'is_public' => false,
                ],
                [
                    'key' => 'enable_user_registration',
                    'value' => '1',
                    'type' => 'boolean',
                    'group' => 'general',
                    'description' => 'Allow new users to register',
                    'is_public' => false,
                ],
                [
                    'key' => 'app_version',
                    'value' => '1.3',
                    'type' => 'string',
                    'group' => 'general',
                    'description' => 'Application version number',
                    'is_public' => true,
                ],
            ],
            'email' => [
                [
                    'key' => 'daily_quote_enabled',
                    'value' => '0',
                    'type' => 'boolean',
                    'group' => 'email',
                    'description' => 'Send daily quote emails to subscribers',
                    'is_public' => false,
                ],
                [
                    'key' => 'daily_quote_time',
                    'value' => '08:00',
                    'type' => 'string',
                    'group' => 'email',
                    'description' => 'Time to send daily quote emails (24-hour format)',
                    'is_public' => false,
                ],
                [
                    'key' => 'admin_email',
                    'value' => '<EMAIL>',
                    'type' => 'string',
                    'group' => 'email',
                    'description' => 'Administrator email address',
                    'is_public' => false,
                ],
            ],
            'appearance' => [
                [
                    'key' => 'theme_color',
                    'value' => 'blue',
                    'type' => 'string',
                    'group' => 'appearance',
                    'description' => 'Primary theme color',
                    'is_public' => true,
                ],
                [
                    'key' => 'enable_dark_mode',
                    'value' => '1',
                    'type' => 'boolean',
                    'group' => 'appearance',
                    'description' => 'Allow users to switch to dark mode',
                    'is_public' => true,
                ],
                [
                    'key' => 'show_author_on_cards',
                    'value' => '1',
                    'type' => 'boolean',
                    'group' => 'appearance',
                    'description' => 'Display author name on quote cards',
                    'is_public' => true,
                ],
            ],
        ];
    }
}
