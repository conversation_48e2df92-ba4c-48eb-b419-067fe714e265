import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Search, Plus, Edit, Trash2, Eye, FileText, Volume2, Star } from 'lucide-react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

export default function AdminQuotesIndex({ quotes = { data: [], total: 0, links: [] }, categories = [], filters = {} }) {
    // Ensure filters is an object, not an array
    const safeFilters = Array.isArray(filters) ? {} : filters;

    const [searchTerm, setSearchTerm] = useState(safeFilters?.search || '');
    const [selectedCategory, setSelectedCategory] = useState(safeFilters?.category || '');
    const [statusFilter, setStatusFilter] = useState(safeFilters?.status || '');

    const handleSearch = (e) => {
        e.preventDefault();
        router.get('/admin/quotes', {
            search: searchTerm,
            category: selectedCategory,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDelete = (quote) => {
        if (confirm(`Are you sure you want to delete the quote "${quote.text?.substring(0, 50) || 'this quote'}..."?`)) {
            router.delete(`/admin/quotes/${quote.id}`);
        }
    };

    const toggleFeatured = (quote) => {
        router.patch(`/admin/quotes/${quote.id}/toggle-featured`, {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    return (
        <AdminLayout>
            <Head title="Manage Quotes - Admin Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <p>
                            Create, edit, and manage all quotes in the system ({quotes?.total || 0} total)
                        </p>
                    </div>
                    <Link href="/admin/quotes/create">
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Quote
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <Input
                                        type="text"
                                        placeholder="Search quotes..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>

                                <div>
                                    <select
                                        value={selectedCategory}
                                        onChange={(e) => setSelectedCategory(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Categories</option>
                                        {categories?.map((category) => (
                                            <option key={category.id} value={category.slug}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <select
                                        value={statusFilter}
                                        onChange={(e) => setStatusFilter(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Status</option>
                                        <option value="featured">Featured</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>

                                <Button type="submit">
                                    <Search className="h-4 w-4 mr-2" />
                                    Search
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Quotes List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quotes ({quotes?.total || 0})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {quotes?.data?.length > 0 ? quotes.data.map((quote) => (
                                <div key={quote.id} className="border rounded-lg p-6">
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-2 mb-2">
                                                <span className="text-sm text-gray-500">#{quote.id}</span>
                                                {quote.is_featured && (
                                                    <Badge className="bg-yellow-100 text-yellow-800">
                                                        <Star className="h-3 w-3 mr-1" />
                                                        Featured
                                                    </Badge>
                                                )}
                                                {quote.category && (
                                                    <Badge variant="outline">{quote.category.name}</Badge>
                                                )}
                                                {quote.pdf_path && (
                                                    <Badge variant="secondary">
                                                        <FileText className="h-3 w-3 mr-1" />
                                                        PDF
                                                    </Badge>
                                                )}
                                                {quote.audio_path && (
                                                    <Badge variant="secondary">
                                                        <Volume2 className="h-3 w-3 mr-1" />
                                                        Audio
                                                    </Badge>
                                                )}
                                            </div>

                                            <blockquote className="text-lg italic text-gray-800 dark:text-gray-200 mb-3">
                                                "{quote.text}"
                                            </blockquote>

                                            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                                {quote.reference && (
                                                    <p><strong>Reference:</strong> {quote.reference}</p>
                                                )}
                                                {quote.author && (
                                                    <p><strong>Author:</strong> {quote.author}</p>
                                                )}
                                                {quote.source && (
                                                    <p><strong>Source:</strong> {quote.source}</p>
                                                )}
                                                <p><strong>Created:</strong> {quote.created_at ? new Date(quote.created_at).toLocaleDateString() : 'Unknown'}</p>
                                                {quote.creator && (
                                                    <p><strong>Created by:</strong> {quote.creator.name}</p>
                                                )}
                                            </div>

                                            {quote.tags && Array.isArray(quote.tags) && quote.tags.length > 0 && (
                                                <div className="flex flex-wrap gap-1 mt-3">
                                                    {quote.tags.map((tag, index) => (
                                                        <Badge key={index} variant="outline" className="text-xs">
                                                            {tag}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex flex-col space-y-2 ml-4">
                                            <Link href={`/quotes/${quote.id}`}>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    View
                                                </Button>
                                            </Link>

                                            <Link href={`/admin/quotes/${quote.id}/edit`}>
                                                <Button variant="outline" size="sm" className="w-full">
                                                    <Edit className="h-4 w-4 mr-2" />
                                                    Edit
                                                </Button>
                                            </Link>

                                            <Button
                                                variant={quote.is_featured ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => toggleFeatured(quote)}
                                                className="w-full"
                                            >
                                                <Star className="h-4 w-4 mr-2" />
                                                {quote.is_featured ? 'Unfeature' : 'Feature'}
                                            </Button>

                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleDelete(quote)}
                                                className="w-full"
                                            >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Delete
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )) : (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">No quotes found</p>
                                    <Link href="/admin/quotes/create" className="mt-4 inline-block">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Create Your First Quote
                                        </Button>
                                    </Link>
                                </div>
                            )}

                            {/* Remove the duplicate empty state since we now handle it in the ternary */}
                            {false && (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">No quotes found</p>
                                    <Link href="/admin/quotes/create" className="mt-4 inline-block">
                                        <Button>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Create Your First Quote
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </div>

                        {/* Pagination */}
                        {quotes?.links && quotes.links.length > 3 && (
                            <div className="flex justify-center mt-6">
                                <div className="flex space-x-2">
                                    {quotes.links.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            className={`px-3 py-2 text-sm rounded-md ${link.active
                                                ? 'bg-blue-600 text-white'
                                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
