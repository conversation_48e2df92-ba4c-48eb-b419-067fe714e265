<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Database\Seeder;

class QuoteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create an admin user if it doesn't exist
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        $categories = Category::all()->keyBy('slug');

        $quotes = [
            [
                'text' => 'For I know the plans I have for you, declares the Lord, plans for welfare and not for evil, to give you a future and a hope.',
                'source' => 'Bible',
                'author' => null,
                'reference' => 'Jeremiah 29:11',
                'category_id' => $categories['hope']->id,
                'tags' => ['future', 'plans', 'hope'],
                'is_featured' => true,
            ],
            [
                'text' => 'Trust in the Lord with all your heart, and do not lean on your own understanding.',
                'source' => 'Bible',
                'author' => null,
                'reference' => 'Proverbs 3:5',
                'category_id' => $categories['faith']->id,
                'tags' => ['trust', 'understanding', 'heart'],
                'is_featured' => true,
            ],
            [
                'text' => 'And we know that for those who love God all things work together for good, for those who are called according to his purpose.',
                'source' => 'Bible',
                'author' => null,
                'reference' => 'Romans 8:28',
                'category_id' => $categories['love']->id,
                'tags' => ['purpose', 'good', 'love'],
                'is_featured' => false,
            ],
            [
                'text' => 'Be anxious for nothing, but in everything by prayer and supplication with thanksgiving let your requests be made known to God.',
                'source' => 'Bible',
                'author' => null,
                'reference' => 'Philippians 4:6',
                'category_id' => $categories['prayer']->id,
                'tags' => ['anxiety', 'thanksgiving', 'requests'],
                'is_featured' => false,
            ],
            [
                'text' => 'Peace I leave with you; my peace I give to you. Not as the world gives do I give to you. Let not your hearts be troubled, neither let them be afraid.',
                'source' => 'Bible',
                'author' => 'Jesus Christ',
                'reference' => 'John 14:27',
                'category_id' => $categories['peace']->id,
                'tags' => ['peace', 'trouble', 'fear'],
                'is_featured' => true,
            ],
            [
                'text' => 'The fear of the Lord is the beginning of wisdom, and the knowledge of the Holy One is insight.',
                'source' => 'Bible',
                'author' => null,
                'reference' => 'Proverbs 9:10',
                'category_id' => $categories['wisdom']->id,
                'tags' => ['fear', 'knowledge', 'insight'],
                'is_featured' => false,
            ],
            [
                'text' => 'I can do all things through him who strengthens me.',
                'source' => 'Bible',
                'author' => 'Paul the Apostle',
                'reference' => 'Philippians 4:13',
                'category_id' => $categories['strength']->id,
                'tags' => ['strength', 'ability', 'Christ'],
                'is_featured' => true,
            ],
            [
                'text' => 'Rejoice in the Lord always; again I will say, rejoice.',
                'source' => 'Bible',
                'author' => 'Paul the Apostle',
                'reference' => 'Philippians 4:4',
                'category_id' => $categories['joy']->id,
                'tags' => ['rejoice', 'always', 'Lord'],
                'is_featured' => false,
            ],
            [
                'text' => 'If we confess our sins, he is faithful and just to forgive us our sins and to cleanse us from all unrighteousness.',
                'source' => 'Bible',
                'author' => 'John the Apostle',
                'reference' => '1 John 1:9',
                'category_id' => $categories['forgiveness']->id,
                'tags' => ['confess', 'faithful', 'cleanse'],
                'is_featured' => false,
            ],
            [
                'text' => 'For God so loved the world, that he gave his only Son, that whoever believes in him should not perish but have eternal life.',
                'source' => 'Bible',
                'author' => 'Jesus Christ',
                'reference' => 'John 3:16',
                'category_id' => $categories['salvation']->id,
                'tags' => ['love', 'eternal life', 'believe'],
                'is_featured' => true,
            ],
        ];

        foreach ($quotes as $quote) {
            Quote::create(array_merge($quote, ['created_by' => $admin->id]));
        }
    }
}
