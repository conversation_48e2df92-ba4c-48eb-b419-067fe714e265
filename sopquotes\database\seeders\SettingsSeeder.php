<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'SOP Quotes',
                'type' => 'string',
                'group' => 'general',
                'description' => 'The name of your website',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'Inspirational quotes from the Spirit of Prophecy',
                'type' => 'string',
                'group' => 'general',
                'description' => 'A brief description of your website',
                'is_public' => true,
            ],
            [
                'key' => 'quotes_per_page',
                'value' => '12',
                'type' => 'integer',
                'group' => 'general',
                'description' => 'Number of quotes to display per page',
                'is_public' => false,
            ],
            [
                'key' => 'enable_user_registration',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'general',
                'description' => 'Allow new users to register',
                'is_public' => false,
            ],
            [
                'key' => 'app_version',
                'value' => '1.3',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Application version number',
                'is_public' => true,
            ],

            // Email Settings
            [
                'key' => 'daily_quote_enabled',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'email',
                'description' => 'Send daily quote emails to subscribers',
                'is_public' => false,
            ],
            [
                'key' => 'daily_quote_time',
                'value' => '08:00',
                'type' => 'string',
                'group' => 'email',
                'description' => 'Time to send daily quote emails (24-hour format)',
                'is_public' => false,
            ],
            [
                'key' => 'admin_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'email',
                'description' => 'Administrator email address',
                'is_public' => false,
            ],

            // Appearance Settings
            [
                'key' => 'theme_color',
                'value' => 'blue',
                'type' => 'string',
                'group' => 'appearance',
                'description' => 'Primary theme color',
                'is_public' => true,
            ],
            [
                'key' => 'enable_dark_mode',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'appearance',
                'description' => 'Allow users to switch to dark mode',
                'is_public' => true,
            ],
            [
                'key' => 'show_author_on_cards',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'appearance',
                'description' => 'Display author name on quote cards',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
