import{j as e,L as g,$ as s}from"./app-BWHLaRS2.js";import{Q as p}from"./quotes-layout-D8XO_JXV.js";import{C as t,a,b as i,c as l}from"./card-3ac6awgC.js";import{B as o}from"./badge-BSmw6aR2.js";import{S as j}from"./settings-FGMHgWsK.js";import{P as u}from"./palette-Cq6FYu77.js";import{C as f}from"./chevron-right-fzPpalsz.js";import{I as y}from"./flash-message-BOB8jroJ.js";import{U as N}from"./user-CmroWLXK.js";import"./button-CUovRkQ3.js";import"./book-open-DxiZ0b6m.js";import"./menu-DfGfLdRU.js";import"./index-CB7we0-r.js";function q({auth:n,appVersion:x="1.0.0"}){var c,d;const m=[{title:"Appearance",description:"Customize the look and feel of the application",icon:u,href:"/settings/appearance",color:"from-purple-500 to-purple-600"}];return e.jsxs(p,{children:[e.jsx(g,{title:"Settings - SOP Quotes"}),e.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-600 to-indigo-600",children:e.jsx(j,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Settings"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Manage your account and application preferences"})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:m.map((r,h)=>e.jsx(s,{href:r.href,children:e.jsxs(t,{className:"h-full hover:shadow-lg transition-shadow duration-200 cursor-pointer group",children:[e.jsx(a,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br ${r.color} shadow-md group-hover:shadow-lg transition-shadow`,children:e.jsx(r.icon,{className:"h-6 w-6 text-white"})}),e.jsx("div",{className:"flex-1",children:e.jsx(i,{className:"text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:r.title})}),e.jsx(f,{className:"h-5 w-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors"})]})}),e.jsx(l,{children:e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:r.description})})]})},h))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(t,{children:[e.jsx(a,{children:e.jsxs(i,{className:"flex items-center space-x-2",children:[e.jsx(y,{className:"h-5 w-5 text-blue-600"}),e.jsx("span",{children:"Application Information"})]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Version"}),e.jsx(o,{variant:"outline",children:x})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Platform"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:"SOP Quotes"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Framework"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:"Laravel + React"})]}),e.jsx("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsx(s,{href:"/about",className:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium transition-colors",children:"Learn more about SOP Quotes →"})})]})]}),e.jsxs(t,{children:[e.jsx(a,{children:e.jsxs(i,{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{children:"Session Information"})]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Access Type"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:"Public Access"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Authentication"}),e.jsx(o,{variant:"secondary",children:"Not Required"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Features"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:"Full Access"})]}),e.jsx("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"All features are available without creating an account. Your favorites are saved in your browser session."})})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsxs(t,{children:[e.jsx(a,{children:e.jsx(i,{children:"Quick Actions"})}),e.jsx(l,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[((c=n.user)==null?void 0:c.role)==="admin"&&e.jsx(s,{href:"/admin/quotes/create",className:"flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:"Create Quote"}),e.jsx(s,{href:"/quotes",className:"flex items-center justify-center px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Browse Quotes"}),e.jsx(s,{href:"/about",className:"flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",children:"About Us"}),((d=n.user)==null?void 0:d.role)==="admin"&&e.jsx(s,{href:"/admin",className:"flex items-center justify-center px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors",children:"Admin Dashboard"})]})})]})})]})})]})}export{q as default};
