import{r as N,m as A,j as e,L as D,$ as _}from"./app-BWHLaRS2.js";import{A as M}from"./admin-layout-DKXm94gZ.js";import{B as o}from"./button-CUovRkQ3.js";import{C as x,a as h,b as p,c as m}from"./card-3ac6awgC.js";import{I as k}from"./input-B4rX1XfI.js";import{L as c}from"./label-DWihlnjb.js";import{T as E,S as L}from"./textarea-DsrDZwdm.js";import{C}from"./checkbox-B6mGW3Gq.js";import{B as z}from"./badge-BSmw6aR2.js";import{A as $}from"./arrow-left-B-2ALS0F.js";import{P as I}from"./plus-CzNwqLHA.js";import{X as P}from"./flash-message-BOB8jroJ.js";import"./sun-Bmefm2yw.js";import"./book-open-DxiZ0b6m.js";import"./volume-2-DIbtoXia.js";import"./users-BHOsrKYW.js";import"./settings-FGMHgWsK.js";import"./menu-DfGfLdRU.js";import"./index-C3-VNe4Y.js";import"./index-CB7we0-r.js";import"./index-BDjL_iy4.js";function de({media:t,categories:j=[],auth:U}){const[r,g]=N.useState(t.tags||[]),[n,f]=N.useState(""),{data:i,setData:d,patch:w,processing:y,errors:a}=A({title:t.title||"",description:t.description||"",category_id:t.category_id?t.category_id.toString():"",tags:t.tags||[],is_featured:t.is_featured||!1,is_active:t.is_active!==void 0?t.is_active:!0}),b=s=>{s.preventDefault(),w(`/admin/media/${t.id}`,{data:{...i,tags:r}})},u=()=>{n.trim()&&!r.includes(n.trim())&&(g([...r,n.trim()]),f(""))},S=s=>{g(r.filter(l=>l!==s))},F=s=>{if(s===0)return"0 Bytes";const l=1024,T=["Bytes","KB","MB","GB"],v=Math.floor(Math.log(s)/Math.log(l));return parseFloat((s/Math.pow(l,v)).toFixed(2))+" "+T[v]},B=s=>new Date(s).toLocaleDateString();return e.jsxs(M,{children:[e.jsx(D,{title:`Edit ${t.title} - Admin Dashboard`}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(_,{href:"/admin/media",children:e.jsxs(o,{variant:"outline",size:"sm",children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Back to Media"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Edit Media"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Update media file information"})]})]})}),e.jsx("form",{onSubmit:b,className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2 space-y-6",children:e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(p,{children:"Basic Information"})}),e.jsxs(m,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"title",children:"Title *"}),e.jsx(k,{id:"title",type:"text",value:i.title,onChange:s=>d("title",s.target.value),className:a.title?"border-red-500":"",placeholder:"Enter media title..."}),a.title&&e.jsx("p",{className:"text-red-500 text-sm",children:a.title})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"description",children:"Description"}),e.jsx(E,{id:"description",value:i.description,onChange:s=>d("description",s.target.value),className:a.description?"border-red-500":"",placeholder:"Enter media description...",rows:4}),a.description&&e.jsx("p",{className:"text-red-500 text-sm",children:a.description})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"category",children:"Category"}),e.jsxs("select",{id:"category",value:i.category_id,onChange:s=>d("category_id",s.target.value),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${a.category_id?"border-red-500":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select a category"}),Array.isArray(j)&&j.map(s=>e.jsx("option",{value:s.id.toString(),children:s.name},s.id))]}),a.category_id&&e.jsx("p",{className:"text-red-500 text-sm",children:a.category_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"tags",children:"Tags"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(k,{type:"text",value:n,onChange:s=>f(s.target.value),placeholder:"Add a tag...",onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),u())}}),e.jsx(o,{type:"button",onClick:u,variant:"outline",children:e.jsx(I,{className:"h-4 w-4"})})]}),r.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:r.map((s,l)=>e.jsxs(z,{variant:"secondary",className:"flex items-center space-x-1",children:[e.jsx("span",{children:s}),e.jsx("button",{type:"button",onClick:()=>S(s),className:"ml-1 hover:text-red-500",children:e.jsx(P,{className:"h-3 w-3"})})]},l))})]})]})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(p,{children:"File Information"})}),e.jsxs(m,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"File Name"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.original_filename})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"File Type"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.file_type.toUpperCase()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"File Size"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:F(t.file_size)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Upload Date"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:B(t.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Views"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.view_count||0})]})]})]}),e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(p,{children:"Settings"})}),e.jsxs(m,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{id:"is_featured",checked:i.is_featured,onCheckedChange:s=>d("is_featured",s)}),e.jsx(c,{htmlFor:"is_featured",children:"Featured Media"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{id:"is_active",checked:i.is_active,onCheckedChange:s=>d("is_active",s)}),e.jsx(c,{htmlFor:"is_active",children:"Active"})]})]})]}),e.jsx(x,{children:e.jsx(m,{className:"pt-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(o,{type:"submit",disabled:y,className:"w-full",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),y?"Saving...":"Save Changes"]}),e.jsx(_,{href:"/admin/media",children:e.jsx(o,{variant:"outline",className:"w-full",children:"Cancel"})})]})})})]})]})})]})]})}export{de as default};
