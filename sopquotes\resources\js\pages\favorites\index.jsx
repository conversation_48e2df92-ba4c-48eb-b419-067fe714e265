import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Heart, BookOpen, Search, Tag, Calendar, FileText, Volume2, Trash2 } from 'lucide-react';
import QuotesLayout from '@/layouts/quotes-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

export default function FavoritesIndex({ quotes, categories, filters, auth }) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [selectedCategory, setSelectedCategory] = useState(filters?.category || '');
    const [favoriteLoading, setFavoriteLoading] = useState({});

    // Safe filters object
    const safeFilters = {
        search: filters?.search || '',
        category: filters?.category || '',
        favorites: '1', // Always show favorites on this page
    };

    const handleSearch = (e) => {
        e.preventDefault();
        router.get('/favorites', {
            search: searchTerm,
            category: selectedCategory,
            favorites: '1',
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleCategoryFilter = (categorySlug) => {
        setSelectedCategory(categorySlug);
        router.get('/favorites', {
            search: searchTerm,
            category: categorySlug,
            favorites: '1',
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const toggleFavorite = async (quoteId) => {
        setFavoriteLoading(prev => ({ ...prev, [quoteId]: true }));

        try {
            const response = await fetch(`/favorites/quote/${quoteId}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            });

            const data = await response.json();

            if (data.success) {
                // Reload the page to update the favorites list
                router.reload({ only: ['quotes'] });
            } else {
                alert('Failed to update favorite status');
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            alert('Failed to update favorite status');
        } finally {
            setFavoriteLoading(prev => ({ ...prev, [quoteId]: false }));
        }
    };

    return (
        <QuotesLayout>
            <Head title="My Favorites - SOP Quotes" />

            <div className="container mx-auto px-4 py-8 max-w-7xl">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        <Heart className="h-8 w-8 inline-block mr-3 text-red-500" />
                        My Favorite Quotes
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                        Your collection of saved inspirational quotes
                    </p>
                </div>

                {/* Search and Filters */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                    <form onSubmit={handleSearch} className="space-y-4">
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="flex-1">
                                <Input
                                    type="text"
                                    placeholder="Search your favorites..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full"
                                />
                            </div>
                            <Button type="submit" className="md:w-auto">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </form>

                    {/* Category Filters */}
                    {categories && categories.length > 0 && (
                        <div className="mt-6">
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                Filter by Category:
                            </h3>
                            <div className="flex flex-wrap gap-2">
                                <Button
                                    variant={selectedCategory === '' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleCategoryFilter('')}
                                >
                                    All Categories
                                </Button>

                                {categories.map((category) => (
                                    <Button
                                        key={category.id}
                                        variant={selectedCategory == category.slug ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleCategoryFilter(category.slug)}
                                        style={{
                                            backgroundColor: selectedCategory == category.slug ? category.color : 'transparent',
                                            borderColor: category.color,
                                            color: selectedCategory == category.slug ? 'white' : category.color,
                                        }}
                                    >
                                        <Tag className="h-3 w-3 mr-1" />
                                        {category.name}
                                    </Button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Quotes Grid */}
                <div className="mb-8">
                    {quotes && quotes.data && quotes.data.length > 0 ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {quotes.data.map((quote) => (
                                    <Card key={quote.id} className="h-full hover:shadow-lg transition-shadow duration-200">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center justify-between mb-2">
                                                {quote.category && (
                                                    <Badge
                                                        variant="secondary"
                                                        className="text-xs"
                                                        style={{ backgroundColor: quote.category.color + '20', color: quote.category.color }}
                                                    >
                                                        {quote.category.name}
                                                    </Badge>
                                                )}
                                                {quote.is_featured && (
                                                    <Badge variant="default" className="text-xs">
                                                        <Heart className="h-3 w-3 mr-1" />
                                                        Featured
                                                    </Badge>
                                                )}
                                            </div>
                                            <blockquote className="text-sm italic text-gray-700 dark:text-gray-300 leading-relaxed">
                                                "{quote.text.length > 120 ? quote.text.substring(0, 120) + '...' : quote.text}"
                                            </blockquote>
                                        </CardHeader>

                                        <CardContent className="space-y-3 pt-0">
                                            {quote.reference && (
                                                <p className="text-xs font-semibold text-blue-600 dark:text-blue-400">
                                                    {quote.reference}
                                                </p>
                                            )}

                                            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                                <div className="flex items-center">
                                                    <Calendar className="h-3 w-3 mr-1" />
                                                    {new Date(quote.created_at).toLocaleDateString()}
                                                </div>
                                            </div>

                                            {/* Download Indicators */}
                                            {(quote.pdf_path || quote.audio_path) && (
                                                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                                    <span>Available:</span>
                                                    {quote.pdf_path && (
                                                        <div className="flex items-center">
                                                            <FileText className="h-3 w-3 mr-1 text-red-500" />
                                                            PDF
                                                        </div>
                                                    )}
                                                    {quote.audio_path && (
                                                        <div className="flex items-center">
                                                            <Volume2 className="h-3 w-3 mr-1 text-blue-500" />
                                                            Audio
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            <div className="flex gap-2">
                                                <Link href={`/quotes/${quote.id}`} className="flex-1">
                                                    <Button variant="outline" size="sm" className="w-full">
                                                        <BookOpen className="h-4 w-4 mr-2" />
                                                        Read Full Quote
                                                    </Button>
                                                </Link>

                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => toggleFavorite(quote.id)}
                                                    disabled={favoriteLoading[quote.id]}
                                                    className="px-3"
                                                    title="Remove from favorites"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>

                            {/* Pagination */}
                            {quotes.links && quotes.links.length > 3 && (
                                <div className="flex justify-center mt-8">
                                    <div className="flex space-x-1">
                                        {quotes.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`px-3 py-2 text-sm rounded-md ${
                                                    link.active
                                                        ? 'bg-blue-600 text-white'
                                                        : link.url
                                                        ? 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                                                        : 'bg-gray-100 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
                                                }`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="text-center py-12">
                            <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                No favorites yet
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                Start adding quotes to your favorites by clicking the heart icon on any quote.
                            </p>
                            <Link href="/quotes">
                                <Button variant="outline">
                                    <BookOpen className="h-4 w-4 mr-2" />
                                    Browse Quotes
                                </Button>
                            </Link>
                        </div>
                    )}
                </div>
            </div>
        </QuotesLayout>
    );
}
